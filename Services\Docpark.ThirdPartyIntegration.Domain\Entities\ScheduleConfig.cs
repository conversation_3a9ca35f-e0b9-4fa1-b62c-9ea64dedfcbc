using System;
using System.ComponentModel.DataAnnotations;
using Docpark.ThirdPartyIntegration.Domain.Enums;

namespace Docpark.ThirdPartyIntegration.Domain.Entities
{
    /// <summary>
    /// 调度配置实体
    /// </summary>
    public class ScheduleConfig
    {
        /// <summary>
        /// 调度类型
        /// </summary>
        [Required]
        public ScheduleType Type { get; set; }

        /// <summary>
        /// 间隔时间（分钟）
        /// </summary>
        public int IntervalMinutes { get; set; }

        /// <summary>
        /// 每日执行时间
        /// </summary>
        public TimeSpan? DailyExecutionTime { get; set; }

        /// <summary>
        /// Cron表达式
        /// </summary>
        [StringLength(100)]
        public string CronExpression { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 下次执行时间
        /// </summary>
        public DateTime? NextExecutionTime { get; set; }

        /// <summary>
        /// 上次执行时间
        /// </summary>
        public DateTime? LastExecutionTime { get; set; }
    }
}
