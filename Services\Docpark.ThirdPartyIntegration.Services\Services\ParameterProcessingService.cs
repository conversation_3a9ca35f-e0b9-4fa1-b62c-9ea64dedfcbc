using System;
using System.Collections.Generic;
using System.Globalization;
using Microsoft.Extensions.Logging;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Domain.Enums;

namespace Docpark.ThirdPartyIntegration.Services.Services
{
    /// <summary>
    /// 参数处理服务
    /// </summary>
    public class ParameterProcessingService
    {
        private readonly ILogger<ParameterProcessingService> _logger;

        public ParameterProcessingService(ILogger<ParameterProcessingService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 处理API参数
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="lastExecutionTime"></param>
        /// <returns></returns>
        public Dictionary<string, string> ProcessParameters(List<ApiParameter> parameters, DateTime? lastExecutionTime = null)
        {
            var processedParams = new Dictionary<string, string>();

            foreach (var param in parameters)
            {
                try
                {
                    var value = ProcessParameter(param, lastExecutionTime);
                    if (!string.IsNullOrEmpty(value))
                    {
                        processedParams[param.Name] = value;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing parameter: {ParameterName}", param.Name);
                    if (param.IsRequired)
                    {
                        throw new InvalidOperationException($"Failed to process required parameter: {param.Name}");
                    }
                }
            }

            return processedParams;
        }

        /// <summary>
        /// 处理单个参数
        /// </summary>
        /// <param name="parameter"></param>
        /// <param name="lastExecutionTime"></param>
        /// <returns></returns>
        private string ProcessParameter(ApiParameter parameter, DateTime? lastExecutionTime)
        {
            switch (parameter.Type)
            {
                case ParameterType.Static:
                    return parameter.Value;

                case ParameterType.CurrentTime:
                    return ProcessCurrentTime(parameter.Format);

                case ParameterType.Timestamp:
                    return ProcessTimestamp(parameter.Format);

                case ParameterType.LastExecutionTime:
                    return ProcessLastExecutionTime(lastExecutionTime, parameter.Format);

                case ParameterType.Dynamic:
                    return ProcessDynamicParameter(parameter);

                default:
                    _logger.LogWarning("Unsupported parameter type: {ParameterType}", parameter.Type);
                    return parameter.Value;
            }
        }

        /// <summary>
        /// 处理当前时间参数
        /// </summary>
        /// <param name="format"></param>
        /// <returns></returns>
        private string ProcessCurrentTime(string format)
        {
            var now = DateTime.UtcNow;
            
            if (string.IsNullOrEmpty(format))
            {
                return now.ToString("yyyy-MM-dd HH:mm:ss");
            }

            // 支持常见的时间格式
            switch (format.ToLower())
            {
                case "iso8601":
                    return now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                case "date":
                    return now.ToString("yyyy-MM-dd");
                case "time":
                    return now.ToString("HH:mm:ss");
                case "timestamp":
                    return ((DateTimeOffset)now).ToUnixTimeSeconds().ToString();
                case "timestamp_ms":
                    return ((DateTimeOffset)now).ToUnixTimeMilliseconds().ToString();
                default:
                    return now.ToString(format);
            }
        }

        /// <summary>
        /// 处理时间戳参数
        /// </summary>
        /// <param name="format"></param>
        /// <returns></returns>
        private string ProcessTimestamp(string format)
        {
            var now = DateTimeOffset.UtcNow;
            
            if (string.IsNullOrEmpty(format) || format.ToLower() == "seconds")
            {
                return now.ToUnixTimeSeconds().ToString();
            }
            else if (format.ToLower() == "milliseconds")
            {
                return now.ToUnixTimeMilliseconds().ToString();
            }
            else
            {
                return now.ToUnixTimeSeconds().ToString();
            }
        }

        /// <summary>
        /// 处理上次执行时间参数
        /// </summary>
        /// <param name="lastExecutionTime"></param>
        /// <param name="format"></param>
        /// <returns></returns>
        private string ProcessLastExecutionTime(DateTime? lastExecutionTime, string format)
        {
            if (!lastExecutionTime.HasValue)
            {
                // 如果没有上次执行时间，使用当前时间减去1小时作为默认值
                lastExecutionTime = DateTime.UtcNow.AddHours(-1);
            }

            if (string.IsNullOrEmpty(format))
            {
                return lastExecutionTime.Value.ToString("yyyy-MM-dd HH:mm:ss");
            }

            switch (format.ToLower())
            {
                case "iso8601":
                    return lastExecutionTime.Value.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                case "date":
                    return lastExecutionTime.Value.ToString("yyyy-MM-dd");
                case "time":
                    return lastExecutionTime.Value.ToString("HH:mm:ss");
                case "timestamp":
                    return ((DateTimeOffset)lastExecutionTime.Value).ToUnixTimeSeconds().ToString();
                case "timestamp_ms":
                    return ((DateTimeOffset)lastExecutionTime.Value).ToUnixTimeMilliseconds().ToString();
                default:
                    return lastExecutionTime.Value.ToString(format);
            }
        }

        /// <summary>
        /// 处理动态参数（可扩展）
        /// </summary>
        /// <param name="parameter"></param>
        /// <returns></returns>
        private string ProcessDynamicParameter(ApiParameter parameter)
        {
            // 这里可以根据需要扩展更多的动态参数处理逻辑
            _logger.LogWarning("Dynamic parameter processing not implemented for: {ParameterName}", parameter.Name);
            return parameter.Value;
        }

        /// <summary>
        /// 验证参数值
        /// </summary>
        /// <param name="parameter"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public bool ValidateParameter(ApiParameter parameter, string value)
        {
            if (parameter.IsRequired && string.IsNullOrEmpty(value))
            {
                return false;
            }

            // 这里可以添加更多的验证逻辑
            return true;
        }
    }
}
