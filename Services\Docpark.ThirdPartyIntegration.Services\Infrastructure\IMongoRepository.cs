using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MongoDB.Bson;
using MongoDB.Driver;

namespace Docpark.ThirdPartyIntegration.Services.Infrastructure
{
    /// <summary>
    /// MongoDB仓储接口
    /// </summary>
    public interface IMongoRepository
    {
        /// <summary>
        /// 插入单个文档
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="collectionName"></param>
        /// <param name="document"></param>
        /// <returns></returns>
        Task InsertOneAsync<T>(string collectionName, T document);

        /// <summary>
        /// 查找单个文档
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="collectionName"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        Task<T> FindOneAsync<T>(string collectionName, FilterDefinition<T> filter);

        /// <summary>
        /// 查找文档列表
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="collectionName"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <param name="limit"></param>
        /// <returns></returns>
        Task<List<T>> FindListAsync<T>(string collectionName, FilterDefinition<T> filter, SortDefinition<T> sort = null, int? limit = null);

        /// <summary>
        /// 替换文档
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="collectionName"></param>
        /// <param name="filter"></param>
        /// <param name="document"></param>
        /// <returns></returns>
        Task<ReplaceOneResult> ReplaceOneAsync<T>(string collectionName, FilterDefinition<T> filter, T document);

        /// <summary>
        /// 更新文档
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="collectionName"></param>
        /// <param name="filter"></param>
        /// <param name="update"></param>
        /// <returns></returns>
        Task<UpdateResult> UpdateOneAsync<T>(string collectionName, FilterDefinition<T> filter, UpdateDefinition<T> update);

        /// <summary>
        /// 批量更新文档
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="collectionName"></param>
        /// <param name="filter"></param>
        /// <param name="update"></param>
        /// <returns></returns>
        Task<UpdateResult> UpdateManyAsync<T>(string collectionName, FilterDefinition<T> filter, UpdateDefinition<T> update);

        /// <summary>
        /// 删除单个文档
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="collectionName"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        Task<DeleteResult> DeleteOneAsync<T>(string collectionName, FilterDefinition<T> filter);

        /// <summary>
        /// 删除多个文档
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="collectionName"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        Task<DeleteResult> DeleteManyAsync<T>(string collectionName, FilterDefinition<T> filter);

        /// <summary>
        /// 统计文档数量
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="collectionName"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        Task<long> CountDocumentsAsync<T>(string collectionName, FilterDefinition<T> filter);

        /// <summary>
        /// 聚合查询
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="collectionName"></param>
        /// <param name="pipeline"></param>
        /// <returns></returns>
        Task<List<T>> AggregateAsync<T>(string collectionName, PipelineDefinition<BsonDocument, T> pipeline);

        /// <summary>
        /// 聚合查询（BsonDocument）
        /// </summary>
        /// <param name="collectionName"></param>
        /// <param name="pipeline"></param>
        /// <returns></returns>
        Task<List<BsonDocument>> AggregateAsync(string collectionName, BsonDocument[] pipeline);
    }
}
