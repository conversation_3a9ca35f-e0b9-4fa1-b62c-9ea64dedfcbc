using System.Collections.Generic;
using System.Threading.Tasks;
using Docpark.ThirdPartyIntegration.Domain.Entities;

namespace Docpark.ThirdPartyIntegration.Services.Interfaces
{
    /// <summary>
    /// 授权配置服务接口
    /// </summary>
    public interface IAuthenticationConfigService
    {
        /// <summary>
        /// 获取所有授权配置
        /// </summary>
        /// <returns></returns>
        Task<List<AuthenticationConfig>> GetAllAsync();

        /// <summary>
        /// 根据ID获取授权配置
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<AuthenticationConfig> GetByIdAsync(string id);

        /// <summary>
        /// 创建授权配置
        /// </summary>
        /// <param name="config"></param>
        /// <returns></returns>
        Task<AuthenticationConfig> CreateAsync(AuthenticationConfig config);

        /// <summary>
        /// 更新授权配置
        /// </summary>
        /// <param name="config"></param>
        /// <returns></returns>
        Task<AuthenticationConfig> UpdateAsync(AuthenticationConfig config);

        /// <summary>
        /// 删除授权配置
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<bool> DeleteAsync(string id);

        /// <summary>
        /// 测试授权配置
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<bool> TestAuthenticationAsync(string id);

        /// <summary>
        /// 启用/禁用授权配置
        /// </summary>
        /// <param name="id"></param>
        /// <param name="enabled"></param>
        /// <returns></returns>
        Task<bool> SetEnabledAsync(string id, bool enabled);
    }
}
