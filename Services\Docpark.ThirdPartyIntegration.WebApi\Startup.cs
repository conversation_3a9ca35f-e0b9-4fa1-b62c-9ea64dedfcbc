using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using Docpark.ThirdPartyIntegration.Services.Interfaces;
using Docpark.ThirdPartyIntegration.Services.Services;
using Docpark.ThirdPartyIntegration.Services.Infrastructure;
using Docpark.ThirdPartyIntegration.Services.Jobs;
using DocPark.MongoDb;
using System.Net.Http;
using Quartz;
using Quartz.Impl;
using Quartz.Spi;

namespace Docpark.ThirdPartyIntegration.WebApi
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        public void ConfigureServices(IServiceCollection services)
        {
            services.AddControllers()
                .AddNewtonsoftJson(options =>
                {
                    options.SerializerSettings.DateTimeZoneHandling = Newtonsoft.Json.DateTimeZoneHandling.Utc;
                    options.SerializerSettings.NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore;
                });

            // MongoDB配置
            services.AddSingleton<IMongodbManager>(provider =>
            {
                return new MongodbManager(Configuration);
            });

            // MongoDB仓储
            services.AddScoped<IMongoRepository, MongoRepository>();

            // HTTP客户端
            services.AddHttpClient();
            services.AddScoped<HttpClient>(provider => provider.GetRequiredService<IHttpClientFactory>().CreateClient());

            // 注册服务
            services.AddScoped<IAuthenticationConfigService, AuthenticationConfigService>();
            services.AddScoped<IAuthenticationService, AuthenticationService>();
            services.AddScoped<IApiConfigurationService, ApiConfigurationService>();
            services.AddScoped<IApiExecutionService, ApiExecutionService>();
            services.AddScoped<IApiExecutionLogService, ApiExecutionLogService>();
            services.AddScoped<IApiResponseDataService, ApiResponseDataService>();
            services.AddScoped<ParameterProcessingService>();

            // 第二阶段新增服务
            services.AddScoped<IDataMappingService, DataMappingService>();
            services.AddScoped<IResponseProcessingService, ResponseProcessingService>();

            // 配置Quartz.NET
            services.AddQuartz(q =>
            {
                q.UseMicrosoftDependencyInjectionJobFactory();
                q.UseSimpleTypeLoader();
                q.UseInMemoryStore();
                q.UseDefaultThreadPool(tp =>
                {
                    tp.MaxConcurrency = 10;
                });
            });

            // 添加Quartz托管服务
            services.AddQuartzHostedService(q => q.WaitForJobsToComplete = true);

            // 注册调度服务
            services.AddScoped<ISchedulerService, SchedulerService>();

            // 注册任务类
            services.AddScoped<ApiExecutionJob>();

            // Swagger配置
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo
                {
                    Title = "Third Party Integration API",
                    Version = "v1",
                    Description = "API for managing third-party integrations"
                });

                // 添加XML注释
                var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
                var xmlPath = System.IO.Path.Combine(System.AppContext.BaseDirectory, xmlFile);
                if (System.IO.File.Exists(xmlPath))
                {
                    c.IncludeXmlComments(xmlPath);
                }
            });

            // CORS配置
            services.AddCors(options =>
            {
                options.AddPolicy("AllowAll", builder =>
                {
                    builder.AllowAnyOrigin()
                           .AllowAnyMethod()
                           .AllowAnyHeader();
                });
            });
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                app.UseSwagger();
                app.UseSwaggerUI(c =>
                {
                    c.SwaggerEndpoint("/swagger/v1/swagger.json", "Third Party Integration API V1");
                    c.RoutePrefix = string.Empty; // 设置Swagger UI为根路径
                });
            }

            app.UseRouting();
            app.UseCors("AllowAll");
            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }
    }
}
