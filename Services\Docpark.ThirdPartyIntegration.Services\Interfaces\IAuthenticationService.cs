using System.Threading.Tasks;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Services.Models;

namespace Docpark.ThirdPartyIntegration.Services.Interfaces
{
    /// <summary>
    /// 授权服务接口
    /// </summary>
    public interface IAuthenticationService
    {
        /// <summary>
        /// 执行授权
        /// </summary>
        /// <param name="config"></param>
        /// <returns></returns>
        Task<AuthenticationResult> AuthenticateAsync(AuthenticationConfig config);

        /// <summary>
        /// 验证令牌
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        Task<bool> ValidateTokenAsync(string token);

        /// <summary>
        /// 刷新令牌
        /// </summary>
        /// <param name="refreshToken"></param>
        /// <returns></returns>
        Task<string> RefreshTokenAsync(string refreshToken);

        /// <summary>
        /// 获取授权头信息
        /// </summary>
        /// <param name="config"></param>
        /// <returns></returns>
        Task<string> GetAuthorizationHeaderAsync(AuthenticationConfig config);
    }
}
