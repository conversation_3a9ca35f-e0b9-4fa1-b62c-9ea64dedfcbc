{"format": 1, "restore": {"E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\Services\\Docpark.ThirdPartyIntegration.Services\\Docpark.ThirdPartyIntegration.Services.csproj": {}}, "projects": {"E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\BuildingBlocks\\DocPark.Commons\\DocPark.Commons.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\BuildingBlocks\\DocPark.Commons\\DocPark.Commons.csproj", "projectName": "DocPark.Commons", "projectPath": "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\BuildingBlocks\\DocPark.Commons\\DocPark.Commons.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\BuildingBlocks\\DocPark.Commons\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"IdentityModel": {"target": "Package", "version": "[4.4.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[3.1.9, )"}, "MongoDB.Bson": {"target": "Package", "version": "[2.12.2, )"}, "SharpCompress": {"target": "Package", "version": "[0.28.1, )"}, "System.Text.Encoding.CodePages": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}, "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\BuildingBlocks\\Docpark.HttpClientExtension\\Docpark.HttpClientExtension.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\BuildingBlocks\\Docpark.HttpClientExtension\\Docpark.HttpClientExtension.csproj", "projectName": "Docpark.HttpClientExtension", "projectPath": "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\BuildingBlocks\\Docpark.HttpClientExtension\\Docpark.HttpClientExtension.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\BuildingBlocks\\Docpark.HttpClientExtension\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {"E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\BuildingBlocks\\DocPark.Commons\\DocPark.Commons.csproj": {"projectPath": "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\BuildingBlocks\\DocPark.Commons\\DocPark.Commons.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"Google.Protobuf": {"target": "Package", "version": "[3.13.0, )"}, "Grpc.AspNetCore": {"target": "Package", "version": "[2.32.0, )"}, "Grpc.Net.ClientFactory": {"target": "Package", "version": "[2.32.0, )"}, "Grpc.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.32.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[3.1.9, )"}, "MiniProfiler.AspNetCore.Mvc": {"target": "Package", "version": "[4.2.22, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[12.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}, "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\BuildingBlocks\\DocPark.MongoDb\\DocPark.MongoDb.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\BuildingBlocks\\DocPark.MongoDb\\DocPark.MongoDb.csproj", "projectName": "DocPark.MongoDb", "projectPath": "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\BuildingBlocks\\DocPark.MongoDb\\DocPark.MongoDb.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\BuildingBlocks\\DocPark.MongoDb\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[3.1.9, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[3.1.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[3.1.9, )"}, "MongoDB.Driver": {"target": "Package", "version": "[2.11.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}, "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\Services\\Docpark.ThirdPartyIntegration.Domain\\Docpark.ThirdPartyIntegration.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\Services\\Docpark.ThirdPartyIntegration.Domain\\Docpark.ThirdPartyIntegration.Domain.csproj", "projectName": "Docpark.ThirdPartyIntegration.Domain", "projectPath": "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\Services\\Docpark.ThirdPartyIntegration.Domain\\Docpark.ThirdPartyIntegration.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\Services\\Docpark.ThirdPartyIntegration.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"MongoDB.Driver": {"target": "Package", "version": "[2.19.0, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}, "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\Services\\Docpark.ThirdPartyIntegration.Services\\Docpark.ThirdPartyIntegration.Services.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\Services\\Docpark.ThirdPartyIntegration.Services\\Docpark.ThirdPartyIntegration.Services.csproj", "projectName": "Docpark.ThirdPartyIntegration.Services", "projectPath": "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\Services\\Docpark.ThirdPartyIntegration.Services\\Docpark.ThirdPartyIntegration.Services.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\Services\\Docpark.ThirdPartyIntegration.Services\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {"E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\BuildingBlocks\\Docpark.HttpClientExtension\\Docpark.HttpClientExtension.csproj": {"projectPath": "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\BuildingBlocks\\Docpark.HttpClientExtension\\Docpark.HttpClientExtension.csproj"}, "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\BuildingBlocks\\DocPark.MongoDb\\DocPark.MongoDb.csproj": {"projectPath": "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\BuildingBlocks\\DocPark.MongoDb\\DocPark.MongoDb.csproj"}, "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\Services\\Docpark.ThirdPartyIntegration.Domain\\Docpark.ThirdPartyIntegration.Domain.csproj": {"projectPath": "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\Services\\Docpark.ThirdPartyIntegration.Domain\\Docpark.ThirdPartyIntegration.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[3.1.9, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[3.1.9, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[3.1.9, )"}, "MongoDB.Driver": {"target": "Package", "version": "[2.19.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Net.Http": {"target": "Package", "version": "[4.3.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}}