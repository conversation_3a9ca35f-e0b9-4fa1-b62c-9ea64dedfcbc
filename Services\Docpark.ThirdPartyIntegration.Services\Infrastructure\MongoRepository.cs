using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Driver;
using DocPark.MongoDb;

namespace Docpark.ThirdPartyIntegration.Services.Infrastructure
{
    /// <summary>
    /// MongoDB仓储实现
    /// </summary>
    public class MongoRepository : IMongoRepository
    {
        private readonly IMongodbManager _mongodbManager;
        private readonly ILogger<MongoRepository> _logger;

        public MongoRepository(IMongodbManager mongodbManager, ILogger<MongoRepository> logger)
        {
            _mongodbManager = mongodbManager;
            _logger = logger;
        }

        public async Task InsertOneAsync<T>(string collectionName, T document)
        {
            try
            {
                var collection = _mongodbManager.GetCollection<T>(collectionName);
                await collection.InsertOneAsync(document);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error inserting document to collection: {CollectionName}", collectionName);
                throw;
            }
        }

        public async Task<T> FindOneAsync<T>(string collectionName, FilterDefinition<T> filter)
        {
            try
            {
                var collection = _mongodbManager.GetCollection<T>(collectionName);
                return await collection.Find(filter).FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding document in collection: {CollectionName}", collectionName);
                throw;
            }
        }

        public async Task<List<T>> FindListAsync<T>(string collectionName, FilterDefinition<T> filter, SortDefinition<T> sort = null, int? limit = null)
        {
            try
            {
                var collection = _mongodbManager.GetCollection<T>(collectionName);
                var findFluent = collection.Find(filter);

                if (sort != null)
                {
                    findFluent = findFluent.Sort(sort);
                }

                if (limit.HasValue)
                {
                    findFluent = findFluent.Limit(limit.Value);
                }

                return await findFluent.ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding documents in collection: {CollectionName}", collectionName);
                throw;
            }
        }

        public async Task<ReplaceOneResult> ReplaceOneAsync<T>(string collectionName, FilterDefinition<T> filter, T document)
        {
            try
            {
                var collection = _mongodbManager.GetCollection<T>(collectionName);
                return await collection.ReplaceOneAsync(filter, document);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error replacing document in collection: {CollectionName}", collectionName);
                throw;
            }
        }

        public async Task<UpdateResult> UpdateOneAsync<T>(string collectionName, FilterDefinition<T> filter, UpdateDefinition<T> update)
        {
            try
            {
                var collection = _mongodbManager.GetCollection<T>(collectionName);
                return await collection.UpdateOneAsync(filter, update);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating document in collection: {CollectionName}", collectionName);
                throw;
            }
        }

        public async Task<UpdateResult> UpdateManyAsync<T>(string collectionName, FilterDefinition<T> filter, UpdateDefinition<T> update)
        {
            try
            {
                var collection = _mongodbManager.GetCollection<T>(collectionName);
                return await collection.UpdateManyAsync(filter, update);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating documents in collection: {CollectionName}", collectionName);
                throw;
            }
        }

        public async Task<DeleteResult> DeleteOneAsync<T>(string collectionName, FilterDefinition<T> filter)
        {
            try
            {
                var collection = _mongodbManager.GetCollection<T>(collectionName);
                return await collection.DeleteOneAsync(filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting document from collection: {CollectionName}", collectionName);
                throw;
            }
        }

        public async Task<DeleteResult> DeleteManyAsync<T>(string collectionName, FilterDefinition<T> filter)
        {
            try
            {
                var collection = _mongodbManager.GetCollection<T>(collectionName);
                return await collection.DeleteManyAsync(filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting documents from collection: {CollectionName}", collectionName);
                throw;
            }
        }

        public async Task<long> CountDocumentsAsync<T>(string collectionName, FilterDefinition<T> filter)
        {
            try
            {
                var collection = _mongodbManager.GetCollection<T>(collectionName);
                return await collection.CountDocumentsAsync(filter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error counting documents in collection: {CollectionName}", collectionName);
                throw;
            }
        }

        public async Task<List<T>> AggregateAsync<T>(string collectionName, PipelineDefinition<BsonDocument, T> pipeline)
        {
            try
            {
                var collection = _mongodbManager.GetCollection<BsonDocument>(collectionName);
                return await collection.Aggregate(pipeline).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error aggregating documents in collection: {CollectionName}", collectionName);
                throw;
            }
        }

        public async Task<List<BsonDocument>> AggregateAsync(string collectionName, BsonDocument[] pipeline)
        {
            try
            {
                var collection = _mongodbManager.GetCollection<BsonDocument>(collectionName);
                return await collection.Aggregate<BsonDocument>(pipeline).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error aggregating documents in collection: {CollectionName}", collectionName);
                throw;
            }
        }
    }
}
