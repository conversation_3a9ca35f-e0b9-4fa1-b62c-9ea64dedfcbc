using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Domain.Enums;
using Docpark.ThirdPartyIntegration.Services.Interfaces;
using Docpark.ThirdPartyIntegration.Services.Models;

namespace Docpark.ThirdPartyIntegration.Services.Services
{
    /// <summary>
    /// API执行服务实现
    /// </summary>
    public class ApiExecutionService : IApiExecutionService
    {
        private readonly HttpClient _httpClient;
        private readonly IApiConfigurationService _apiConfigService;
        private readonly IAuthenticationConfigService _authConfigService;
        private readonly IAuthenticationService _authService;
        private readonly ParameterProcessingService _parameterService;
        private readonly IApiExecutionLogService _logService;
        private readonly IApiResponseDataService _responseDataService;
        private readonly ILogger<ApiExecutionService> _logger;

        public ApiExecutionService(
            HttpClient httpClient,
            IApiConfigurationService apiConfigService,
            IAuthenticationConfigService authConfigService,
            IAuthenticationService authService,
            ParameterProcessingService parameterService,
            IApiExecutionLogService logService,
            IApiResponseDataService responseDataService,
            ILogger<ApiExecutionService> logger)
        {
            _httpClient = httpClient;
            _apiConfigService = apiConfigService;
            _authConfigService = authConfigService;
            _authService = authService;
            _parameterService = parameterService;
            _logService = logService;
            _responseDataService = responseDataService;
            _logger = logger;
        }

        public async Task<ApiExecutionResult> ExecuteApiAsync(string apiConfigId)
        {
            return await ExecuteApiInternalAsync(apiConfigId, "Auto");
        }

        public async Task<ApiExecutionResult> ExecuteManuallyAsync(string apiConfigId, string executedBy)
        {
            return await ExecuteApiInternalAsync(apiConfigId, "Manual", executedBy);
        }

        private async Task<ApiExecutionResult> ExecuteApiInternalAsync(string apiConfigId, string executionType, string executedBy = null)
        {
            var stopwatch = Stopwatch.StartNew();
            ApiExecutionLog log = null;

            try
            {
                // 获取API配置
                var apiConfig = await _apiConfigService.GetByIdAsync(apiConfigId);
                if (apiConfig == null)
                {
                    return ApiExecutionResult.Failure(apiConfigId, "API configuration not found");
                }

                if (!apiConfig.IsEnabled)
                {
                    return ApiExecutionResult.Failure(apiConfigId, "API configuration is disabled");
                }

                // 创建执行日志
                log = new ApiExecutionLog
                {
                    ApiConfigId = apiConfigId,
                    ApiName = apiConfig.Name,
                    StartTime = DateTime.UtcNow,
                    Status = ExecutionStatus.Running,
                    ExecutionType = executionType,
                    ExecutedBy = executedBy
                };

                var logId = await _logService.CreateAsync(log);
                log.Id = logId;

                // 获取授权配置
                AuthenticationConfig authConfig = null;
                if (!string.IsNullOrEmpty(apiConfig.AuthenticationConfigId))
                {
                    authConfig = await _authConfigService.GetByIdAsync(apiConfig.AuthenticationConfigId);
                }

                // 处理参数
                var lastExecutionTime = apiConfig.Schedule?.LastExecutionTime;
                var processedParams = _parameterService.ProcessParameters(apiConfig.Parameters, lastExecutionTime);

                // 构建请求
                var request = await BuildHttpRequestAsync(apiConfig, authConfig, processedParams);
                log.RequestData = await SerializeRequestAsync(request);

                // 执行请求
                var response = await _httpClient.SendAsync(request);
                var responseContent = await response.Content.ReadAsStringAsync();

                stopwatch.Stop();

                // 更新日志
                log.EndTime = DateTime.UtcNow;
                log.DurationMs = stopwatch.ElapsedMilliseconds;
                log.StatusCode = (int)response.StatusCode;
                log.ResponseData = responseContent;
                log.Status = response.IsSuccessStatusCode ? ExecutionStatus.Success : ExecutionStatus.Failed;

                if (!response.IsSuccessStatusCode)
                {
                    log.ErrorMessage = $"HTTP {response.StatusCode}: {response.ReasonPhrase}";
                }

                await _logService.UpdateAsync(log);

                // 返回结果
                var result = response.IsSuccessStatusCode
                    ? ApiExecutionResult.Success(apiConfigId, responseContent, (int)response.StatusCode, stopwatch.Elapsed)
                    : ApiExecutionResult.Failure(apiConfigId, log.ErrorMessage, (int)response.StatusCode, stopwatch.Elapsed);

                result.ExecutionLogId = log.Id;
                result.RequestData = log.RequestData;

                // 保存响应数据到MongoDB
                if (result.IsSuccess)
                {
                    try
                    {
                        var responseDataId = await _responseDataService.SaveResponseDataAsync(result);
                        if (!string.IsNullOrEmpty(responseDataId))
                        {
                            _logger.LogDebug("Saved response data with ID: {ResponseDataId}", responseDataId);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error saving response data for API: {ApiConfigId}", apiConfigId);
                        // 不影响主流程，继续返回结果
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Error executing API: {ApiConfigId}", apiConfigId);

                // 更新日志
                if (log != null)
                {
                    log.EndTime = DateTime.UtcNow;
                    log.DurationMs = stopwatch.ElapsedMilliseconds;
                    log.Status = ExecutionStatus.Failed;
                    log.ErrorMessage = ex.Message;
                    await _logService.UpdateAsync(log);
                }

                return ApiExecutionResult.Failure(apiConfigId, ex.Message, 0, stopwatch.Elapsed);
            }
        }

        public async Task<List<ApiExecutionResult>> ExecuteBatchAsync(List<string> apiConfigIds)
        {
            var results = new List<ApiExecutionResult>();
            var tasks = new List<Task<ApiExecutionResult>>();

            foreach (var apiConfigId in apiConfigIds)
            {
                tasks.Add(ExecuteApiAsync(apiConfigId));
            }

            var taskResults = await Task.WhenAll(tasks);
            results.AddRange(taskResults);

            return results;
        }

        public async Task<bool> TestApiConnectionAsync(string apiConfigId)
        {
            try
            {
                var result = await ExecuteApiAsync(apiConfigId);
                return result.IsSuccess;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing API connection: {ApiConfigId}", apiConfigId);
                return false;
            }
        }

        private async Task<HttpRequestMessage> BuildHttpRequestAsync(
            ApiConfiguration apiConfig, 
            AuthenticationConfig authConfig, 
            Dictionary<string, string> processedParams)
        {
            var url = $"{apiConfig.BaseUrl.TrimEnd('/')}/{apiConfig.Endpoint.TrimStart('/')}";
            var method = new HttpMethod(apiConfig.Method.ToUpper());
            var request = new HttpRequestMessage(method, url);

            // 设置超时
            _httpClient.Timeout = TimeSpan.FromSeconds(apiConfig.TimeoutSeconds);

            // 添加授权头
            if (authConfig != null)
            {
                await AddAuthorizationAsync(request, authConfig);
            }

            // 添加参数
            await AddParametersAsync(request, processedParams, apiConfig.Method);

            return request;
        }

        private async Task AddAuthorizationAsync(HttpRequestMessage request, AuthenticationConfig authConfig)
        {
            try
            {
                if (authConfig.Type == AuthenticationType.ApiKey)
                {
                    var result = await _authService.AuthenticateAsync(authConfig);
                    if (result.IsSuccess)
                    {
                        var keyName = result.AdditionalData.GetValueOrDefault("keyName", "X-API-Key");
                        var location = result.AdditionalData.GetValueOrDefault("location", "header");

                        if (location.ToLower() == "header")
                        {
                            request.Headers.Add(keyName, result.AccessToken);
                        }
                        // Query parameter handling will be done in AddParametersAsync
                    }
                }
                else
                {
                    var authHeader = await _authService.GetAuthorizationHeaderAsync(authConfig);
                    request.Headers.Add("Authorization", authHeader);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding authorization to request");
                throw;
            }
        }

        private async Task AddParametersAsync(HttpRequestMessage request, Dictionary<string, string> parameters, string method)
        {
            if (parameters == null || parameters.Count == 0)
                return;

            if (method.ToUpper() == "GET")
            {
                // Add as query parameters
                var queryParams = new List<string>();
                foreach (var param in parameters)
                {
                    queryParams.Add($"{Uri.EscapeDataString(param.Key)}={Uri.EscapeDataString(param.Value)}");
                }

                var queryString = string.Join("&", queryParams);
                var uriBuilder = new UriBuilder(request.RequestUri);
                uriBuilder.Query = string.IsNullOrEmpty(uriBuilder.Query) 
                    ? queryString 
                    : uriBuilder.Query.TrimStart('?') + "&" + queryString;
                request.RequestUri = uriBuilder.Uri;
            }
            else
            {
                // Add as JSON body
                var json = JsonConvert.SerializeObject(parameters);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");
            }
        }

        private async Task<string> SerializeRequestAsync(HttpRequestMessage request)
        {
            var requestInfo = new
            {
                Method = request.Method.ToString(),
                Url = request.RequestUri?.ToString(),
                Headers = request.Headers,
                Content = request.Content != null ? await request.Content.ReadAsStringAsync() : null
            };

            return JsonConvert.SerializeObject(requestInfo, Formatting.Indented);
        }
    }
}
