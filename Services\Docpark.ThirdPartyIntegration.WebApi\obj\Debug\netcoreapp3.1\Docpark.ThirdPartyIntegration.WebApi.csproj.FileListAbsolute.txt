E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\appsettings.Development.json
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\appsettings.json
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Docpark.ThirdPartyIntegration.WebApi.exe
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Docpark.ThirdPartyIntegration.WebApi.deps.json
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Docpark.ThirdPartyIntegration.WebApi.runtimeconfig.json
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Docpark.ThirdPartyIntegration.WebApi.runtimeconfig.dev.json
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Docpark.ThirdPartyIntegration.WebApi.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Docpark.ThirdPartyIntegration.WebApi.pdb
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Autofac.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Autofac.Extensions.DependencyInjection.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\AWSSDK.Core.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\AWSSDK.SecurityToken.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\DnsClient.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Google.Protobuf.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Grpc.AspNetCore.Server.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Grpc.AspNetCore.Server.ClientFactory.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Grpc.Core.Api.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Grpc.Net.Client.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Grpc.Net.ClientFactory.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Grpc.Net.Common.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\IdentityModel.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.JsonPatch.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Abstractions.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Binder.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.FileExtensions.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Json.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.Abstractions.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Microsoft.Extensions.FileProviders.Abstractions.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Microsoft.Extensions.FileProviders.Physical.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Microsoft.Extensions.FileSystemGlobbing.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.Abstractions.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Options.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Primitives.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Microsoft.OpenApi.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Microsoft.Win32.Registry.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\MiniProfiler.AspNetCore.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\MiniProfiler.AspNetCore.Mvc.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\MiniProfiler.Shared.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\MongoDB.Bson.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\MongoDB.Driver.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\MongoDB.Driver.Core.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\MongoDB.Libmongocrypt.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Newtonsoft.Json.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Newtonsoft.Json.Bson.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\SharpCompress.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Snappier.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.Swagger.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.SwaggerGen.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.SwaggerUI.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\System.ComponentModel.Annotations.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\System.Runtime.CompilerServices.Unsafe.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\System.Security.AccessControl.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\System.Security.Principal.Windows.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\System.Text.Encoding.CodePages.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\System.Text.Json.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\ZstdSharp.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\runtimes\win\lib\netstandard2.0\Microsoft.Win32.Registry.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\runtimes\linux\native\libmongocrypt.so
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\runtimes\osx\native\libmongocrypt.dylib
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\runtimes\win\native\mongocrypt.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\runtimes\win\lib\netcoreapp2.0\System.Security.AccessControl.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\runtimes\unix\lib\netcoreapp2.1\System.Security.Principal.Windows.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\runtimes\win\lib\netcoreapp2.1\System.Security.Principal.Windows.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\runtimes\win\lib\netcoreapp2.0\System.Text.Encoding.CodePages.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\DocPark.Commons.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Docpark.HttpClientExtension.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\DocPark.MongoDb.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Docpark.ThirdPartyIntegration.Domain.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Docpark.ThirdPartyIntegration.Services.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Docpark.ThirdPartyIntegration.Services.pdb
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Docpark.ThirdPartyIntegration.Domain.pdb
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\DocPark.Commons.pdb
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\DocPark.MongoDb.pdb
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\bin\Debug\netcoreapp3.1\Docpark.HttpClientExtension.pdb
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\obj\Debug\netcoreapp3.1\Docpark.ThirdPartyIntegration.WebApi.csproj.AssemblyReference.cache
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\obj\Debug\netcoreapp3.1\Docpark.ThirdPartyIntegration.WebApi.GeneratedMSBuildEditorConfig.editorconfig
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\obj\Debug\netcoreapp3.1\Docpark.ThirdPartyIntegration.WebApi.AssemblyInfoInputs.cache
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\obj\Debug\netcoreapp3.1\Docpark.ThirdPartyIntegration.WebApi.AssemblyInfo.cs
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\obj\Debug\netcoreapp3.1\Docpark.ThirdPartyIntegration.WebApi.csproj.CoreCompileInputs.cache
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\obj\Debug\netcoreapp3.1\Docpark.ThirdPartyIntegration.WebApi.MvcApplicationPartsAssemblyInfo.cs
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\obj\Debug\netcoreapp3.1\Docpark.ThirdPartyIntegration.WebApi.MvcApplicationPartsAssemblyInfo.cache
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\obj\Debug\netcoreapp3.1\Docpark.ThirdPartyIntegration.WebApi.RazorTargetAssemblyInfo.cache
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\obj\Debug\netcoreapp3.1\staticwebassets\Docpark.ThirdPartyIntegration.WebApi.StaticWebAssets.Manifest.cache
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\obj\Debug\netcoreapp3.1\staticwebassets\Docpark.ThirdPartyIntegration.WebApi.StaticWebAssets.Pack.cache
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.Docpark.ThirdPartyIntegration.WebApi.Microsoft.AspNetCore.StaticWebAssets.props
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.build.Docpark.ThirdPartyIntegration.WebApi.props
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.buildMultiTargeting.Docpark.ThirdPartyIntegration.WebApi.props
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.buildTransitive.Docpark.ThirdPartyIntegration.WebApi.props
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\obj\Debug\netcoreapp3.1\Docpark..FF5CA13E.Up2Date
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\obj\Debug\netcoreapp3.1\Docpark.ThirdPartyIntegration.WebApi.dll
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\obj\Debug\netcoreapp3.1\Docpark.ThirdPartyIntegration.WebApi.pdb
E:\Work Folder\Projects\Code\Docpark\Docpark.ExtendedFeature\Services\Docpark.ThirdPartyIntegration.WebApi\obj\Debug\netcoreapp3.1\Docpark.ThirdPartyIntegration.WebApi.genruntimeconfig.cache
