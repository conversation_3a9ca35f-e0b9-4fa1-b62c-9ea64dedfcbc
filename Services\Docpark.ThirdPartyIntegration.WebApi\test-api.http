### 第三方接口集成系统 API 测试

### 1. 创建BasicAuth授权配置
POST http://localhost:5000/api/AuthenticationConfig
Content-Type: application/json

{
  "name": "测试BasicAuth",
  "description": "用于测试的基础认证",
  "type": 1,
  "parameters": {
    "username": "test_user",
    "password": "test_password"
  },
  "isEnabled": true
}

### 2. 创建OAuth2授权配置
POST http://localhost:5000/api/AuthenticationConfig
Content-Type: application/json

{
  "name": "测试OAuth2",
  "description": "用于测试的OAuth2认证",
  "type": 2,
  "parameters": {
    "clientId": "test_client_id",
    "clientSecret": "test_client_secret",
    "tokenUrl": "https://httpbin.org/post",
    "scope": "read"
  },
  "isEnabled": true
}

### 3. 获取所有授权配置
GET http://localhost:5000/api/AuthenticationConfig

### 4. 创建API配置
POST http://localhost:5000/api/ApiConfiguration
Content-Type: application/json

{
  "name": "测试API",
  "description": "用于测试的API配置",
  "baseUrl": "https://httpbin.org",
  "endpoint": "/get",
  "method": "GET",
  "authenticationConfigId": "替换为实际的授权配置ID",
  "parameters": [
    {
      "name": "test_param",
      "location": "query",
      "type": 1,
      "value": "test_value",
      "isRequired": false,
      "description": "测试参数"
    },
    {
      "name": "current_time",
      "location": "query",
      "type": 2,
      "format": "yyyy-MM-dd HH:mm:ss",
      "isRequired": false,
      "description": "当前时间参数"
    }
  ],
  "schedule": {
    "type": 1,
    "intervalMinutes": 60,
    "isEnabled": false
  },
  "isEnabled": true,
  "timeoutSeconds": 30,
  "retryCount": 3
}

### 5. 获取所有API配置
GET http://localhost:5000/api/ApiConfiguration

### 6. 手动执行API（需要替换为实际的API配置ID）
POST http://localhost:5000/api/ApiExecution/替换为实际的API配置ID/execute

### 7. 测试API连接（需要替换为实际的API配置ID）
POST http://localhost:5000/api/ApiExecution/替换为实际的API配置ID/test

### 8. 查看API响应数据（需要替换为实际的API配置ID）
GET http://localhost:5000/api/ApiResponseData/by-api/替换为实际的API配置ID?limit=10

### 9. 获取数据统计
GET http://localhost:5000/api/ApiResponseData/statistics

### 10. 查询执行日志
GET http://localhost:5000/api/ApiExecutionLog/query?limit=10

### 11. 创建一个简单的测试API配置（不需要授权）
POST http://localhost:5000/api/ApiConfiguration
Content-Type: application/json

{
  "name": "简单测试API",
  "description": "不需要授权的测试API",
  "baseUrl": "https://httpbin.org",
  "endpoint": "/json",
  "method": "GET",
  "parameters": [],
  "schedule": {
    "type": 1,
    "intervalMinutes": 30,
    "isEnabled": false
  },
  "isEnabled": true,
  "timeoutSeconds": 30,
  "retryCount": 1
}

### 12. 获取启用的API配置
GET http://localhost:5000/api/ApiConfiguration/enabled

### 13. 批量执行API（需要替换为实际的API配置ID数组）
POST http://localhost:5000/api/ApiExecution/batch-execute
Content-Type: application/json

[
  "替换为实际的API配置ID1",
  "替换为实际的API配置ID2"
]

### 14. 清理过期数据（删除30天前的数据）
DELETE http://localhost:5000/api/ApiResponseData/cleanup?olderThanDays=30

### ========== 第二阶段新功能测试 ==========

### 15. 验证数据映射配置
POST http://localhost:5000/api/DataMapping/validate
Content-Type: application/json

{
  "isEnabled": true,
  "rules": [
    {
      "sourcePath": "$.data[*].id",
      "targetField": "user_id",
      "transformType": 1,
      "isRequired": true
    },
    {
      "sourcePath": "$.data[*].name",
      "targetField": "user_name",
      "transformType": 1,
      "defaultValue": "Unknown"
    }
  ],
  "rootPath": "$",
  "keepOriginalData": true,
  "mappedDataName": "mapped_users"
}

### 16. 生成映射预览
POST http://localhost:5000/api/DataMapping/preview
Content-Type: application/json

{
  "sampleData": {
    "data": [
      {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>",
        "created_at": "2023-12-01T10:30:00Z"
      },
      {
        "id": 2,
        "name": "Jane Smith",
        "email": "<EMAIL>",
        "created_at": "2023-12-01T11:00:00Z"
      }
    ]
  },
  "mappingConfig": {
    "isEnabled": true,
    "rules": [
      {
        "sourcePath": "$.data[*].id",
        "targetField": "user_id",
        "transformType": 2,
        "isRequired": true
      },
      {
        "sourcePath": "$.data[*].name",
        "targetField": "user_name",
        "transformType": 1
      },
      {
        "sourcePath": "$.data[*].created_at",
        "targetField": "created_date",
        "transformType": 3,
        "transformParameter": "yyyy-MM-ddTHH:mm:ssZ"
      }
    ],
    "rootPath": "$",
    "keepOriginalData": true,
    "mappedDataName": "mapped_users"
  }
}

### 17. 测试数据转换
POST http://localhost:5000/api/DataMapping/test-transform
Content-Type: application/json

{
  "value": "2023-12-01T10:30:00Z",
  "transformType": 3,
  "parameter": "yyyy-MM-dd"
}

### 18. 测试数据过滤
POST http://localhost:5000/api/ResponseProcessing/test-filter
Content-Type: application/json

{
  "testData": [
    {"id": 1, "name": "John", "age": 25, "status": "active"},
    {"id": 2, "name": "Jane", "age": 30, "status": "inactive"},
    {"id": 3, "name": "Bob", "age": 35, "status": "active"}
  ],
  "filterConfig": {
    "isEnabled": true,
    "conditions": [
      {
        "fieldPath": "$.status",
        "operator": 0,
        "value": "active"
      },
      {
        "fieldPath": "$.age",
        "operator": 3,
        "value": "25"
      }
    ],
    "logicalOperator": 0
  }
}

### 19. 测试数据验证
POST http://localhost:5000/api/ResponseProcessing/test-validation
Content-Type: application/json

{
  "testData": [
    {"id": 1, "email": "<EMAIL>", "age": 25},
    {"id": 2, "email": "invalid-email", "age": -5},
    {"id": 3, "email": "<EMAIL>", "age": 30}
  ],
  "validationConfig": {
    "isEnabled": true,
    "rules": [
      {
        "fieldPath": "$.email",
        "type": 3,
        "isRequired": true,
        "errorMessage": "邮箱格式无效"
      },
      {
        "fieldPath": "$.age",
        "type": 1,
        "parameter": "0-120",
        "isRequired": true,
        "errorMessage": "年龄必须在0-120之间"
      }
    ],
    "failureStrategy": 0
  }
}

### 20. 获取支持的数据转换类型
GET http://localhost:5000/api/DataMapping/transform-types

### 21. 获取支持的过滤操作符
GET http://localhost:5000/api/ResponseProcessing/filter-operators

### 22. 获取支持的聚合类型
GET http://localhost:5000/api/ResponseProcessing/aggregation-types

### 23. 更新API配置的数据映射设置（需要替换为实际的API配置ID）
PUT http://localhost:5000/api/DataMapping/api-config/替换为实际的API配置ID/mapping
Content-Type: application/json

{
  "isEnabled": true,
  "rules": [
    {
      "sourcePath": "$.slideshow.slides[*].title",
      "targetField": "slide_title",
      "transformType": 1,
      "isRequired": false
    },
    {
      "sourcePath": "$.slideshow.slides[*].type",
      "targetField": "slide_type",
      "transformType": 1,
      "defaultValue": "unknown"
    }
  ],
  "rootPath": "$.slideshow",
  "keepOriginalData": true,
  "mappedDataName": "processed_slides"
}

### 24. 获取API配置的数据映射设置（需要替换为实际的API配置ID）
GET http://localhost:5000/api/DataMapping/api-config/替换为实际的API配置ID/mapping

### 25. 更新API配置的响应处理设置（需要替换为实际的API配置ID）
PUT http://localhost:5000/api/ResponseProcessing/api-config/替换为实际的API配置ID/processing
Content-Type: application/json

{
  "isEnabled": true,
  "deduplication": {
    "isEnabled": true,
    "strategy": 0,
    "keyFields": ["id"],
    "hashAlgorithm": "SHA256",
    "scopeDays": 30
  },
  "filter": {
    "isEnabled": true,
    "conditions": [
      {
        "fieldPath": "$.status",
        "operator": 0,
        "value": "active"
      }
    ],
    "logicalOperator": 0
  },
  "validation": {
    "isEnabled": true,
    "rules": [
      {
        "fieldPath": "$.id",
        "type": 1,
        "parameter": "1-999999",
        "isRequired": true,
        "errorMessage": "ID必须在1-999999之间"
      }
    ],
    "failureStrategy": 1
  },
  "errorHandling": 0,
  "batchProcessing": {
    "isEnabled": false,
    "batchSize": 100,
    "intervalMs": 1000,
    "maxWaitSeconds": 30
  }
}

### ========== 定时任务调度系统测试 ==========

### 26. 启动调度器
POST http://localhost:5000/api/Scheduler/start

### 27. 获取调度器统计信息
GET http://localhost:5000/api/Scheduler/statistics

### 28. 调度API任务（需要替换为实际的API配置ID）
POST http://localhost:5000/api/Scheduler/schedule/替换为实际的API配置ID
Content-Type: application/json

{
  "type": 1,
  "intervalMinutes": 30,
  "isEnabled": true,
  "priority": 5,
  "timeoutMinutes": 10,
  "retryCount": 3,
  "retryIntervalMinutes": 5,
  "allowConcurrentExecution": false,
  "jobGroup": "API_JOBS",
  "description": "定时执行API任务",
  "timeZone": "UTC",
  "misfireInstruction": 0,
  "maxExecutionCount": 0
}

### 29. 使用Cron表达式调度任务（每天上午9点执行）
POST http://localhost:5000/api/Scheduler/schedule/替换为实际的API配置ID
Content-Type: application/json

{
  "type": 3,
  "cronExpression": "0 0 9 * * ?",
  "isEnabled": true,
  "priority": 8,
  "timeoutMinutes": 15,
  "retryCount": 2,
  "allowConcurrentExecution": false,
  "jobGroup": "DAILY_JOBS",
  "description": "每日上午9点执行API任务",
  "timeZone": "Asia/Shanghai",
  "misfireInstruction": 1
}

### 30. 验证Cron表达式
POST http://localhost:5000/api/Scheduler/validate-cron
Content-Type: application/json

{
  "cronExpression": "0 0 9 * * ?"
}

### 31. 获取Cron表达式的下几次执行时间
POST http://localhost:5000/api/Scheduler/cron-next-times
Content-Type: application/json

{
  "cronExpression": "0 */30 * * * ?",
  "count": 10
}

### 32. 获取所有任务状态
GET http://localhost:5000/api/Scheduler/jobs

### 33. 获取特定任务状态（需要替换为实际的任务键）
GET http://localhost:5000/api/Scheduler/job-status/API_JOBS.api-job-替换为实际的API配置ID

### 34. 立即执行任务（需要替换为实际的任务键）
POST http://localhost:5000/api/Scheduler/trigger/API_JOBS.api-job-替换为实际的API配置ID

### 35. 暂停任务（需要替换为实际的任务键）
POST http://localhost:5000/api/Scheduler/pause-job/API_JOBS.api-job-替换为实际的API配置ID

### 36. 恢复任务（需要替换为实际的任务键）
POST http://localhost:5000/api/Scheduler/resume-job/API_JOBS.api-job-替换为实际的API配置ID

### 37. 获取任务执行历史（需要替换为实际的任务键）
GET http://localhost:5000/api/Scheduler/job-history/API_JOBS.api-job-替换为实际的API配置ID?limit=20

### 38. 重新调度任务（需要替换为实际的任务键）
PUT http://localhost:5000/api/Scheduler/reschedule/API_JOBS.api-job-替换为实际的API配置ID
Content-Type: application/json

{
  "type": 1,
  "intervalMinutes": 60,
  "isEnabled": true,
  "priority": 7,
  "timeoutMinutes": 20,
  "retryCount": 5,
  "allowConcurrentExecution": true,
  "jobGroup": "API_JOBS",
  "description": "重新调度的API任务 - 每小时执行一次"
}

### 39. 取消调度任务（需要替换为实际的任务键）
DELETE http://localhost:5000/api/Scheduler/unschedule/API_JOBS.api-job-替换为实际的API配置ID

### 40. 清理过期执行历史（删除30天前的记录）
DELETE http://localhost:5000/api/Scheduler/cleanup-history?olderThanDays=30

### 41. 暂停调度器
POST http://localhost:5000/api/Scheduler/pause

### 42. 恢复调度器
POST http://localhost:5000/api/Scheduler/resume

### 43. 停止调度器
POST http://localhost:5000/api/Scheduler/stop
