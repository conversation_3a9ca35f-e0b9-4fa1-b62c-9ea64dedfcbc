### 第三方接口集成系统 API 测试

### 1. 创建BasicAuth授权配置
POST http://localhost:5000/api/AuthenticationConfig
Content-Type: application/json

{
  "name": "测试BasicAuth",
  "description": "用于测试的基础认证",
  "type": 1,
  "parameters": {
    "username": "test_user",
    "password": "test_password"
  },
  "isEnabled": true
}

### 2. 创建OAuth2授权配置
POST http://localhost:5000/api/AuthenticationConfig
Content-Type: application/json

{
  "name": "测试OAuth2",
  "description": "用于测试的OAuth2认证",
  "type": 2,
  "parameters": {
    "clientId": "test_client_id",
    "clientSecret": "test_client_secret",
    "tokenUrl": "https://httpbin.org/post",
    "scope": "read"
  },
  "isEnabled": true
}

### 3. 获取所有授权配置
GET http://localhost:5000/api/AuthenticationConfig

### 4. 创建API配置
POST http://localhost:5000/api/ApiConfiguration
Content-Type: application/json

{
  "name": "测试API",
  "description": "用于测试的API配置",
  "baseUrl": "https://httpbin.org",
  "endpoint": "/get",
  "method": "GET",
  "authenticationConfigId": "替换为实际的授权配置ID",
  "parameters": [
    {
      "name": "test_param",
      "location": "query",
      "type": 1,
      "value": "test_value",
      "isRequired": false,
      "description": "测试参数"
    },
    {
      "name": "current_time",
      "location": "query",
      "type": 2,
      "format": "yyyy-MM-dd HH:mm:ss",
      "isRequired": false,
      "description": "当前时间参数"
    }
  ],
  "schedule": {
    "type": 1,
    "intervalMinutes": 60,
    "isEnabled": false
  },
  "isEnabled": true,
  "timeoutSeconds": 30,
  "retryCount": 3
}

### 5. 获取所有API配置
GET http://localhost:5000/api/ApiConfiguration

### 6. 手动执行API（需要替换为实际的API配置ID）
POST http://localhost:5000/api/ApiExecution/替换为实际的API配置ID/execute

### 7. 测试API连接（需要替换为实际的API配置ID）
POST http://localhost:5000/api/ApiExecution/替换为实际的API配置ID/test

### 8. 查看API响应数据（需要替换为实际的API配置ID）
GET http://localhost:5000/api/ApiResponseData/by-api/替换为实际的API配置ID?limit=10

### 9. 获取数据统计
GET http://localhost:5000/api/ApiResponseData/statistics

### 10. 查询执行日志
GET http://localhost:5000/api/ApiExecutionLog/query?limit=10

### 11. 创建一个简单的测试API配置（不需要授权）
POST http://localhost:5000/api/ApiConfiguration
Content-Type: application/json

{
  "name": "简单测试API",
  "description": "不需要授权的测试API",
  "baseUrl": "https://httpbin.org",
  "endpoint": "/json",
  "method": "GET",
  "parameters": [],
  "schedule": {
    "type": 1,
    "intervalMinutes": 30,
    "isEnabled": false
  },
  "isEnabled": true,
  "timeoutSeconds": 30,
  "retryCount": 1
}

### 12. 获取启用的API配置
GET http://localhost:5000/api/ApiConfiguration/enabled

### 13. 批量执行API（需要替换为实际的API配置ID数组）
POST http://localhost:5000/api/ApiExecution/batch-execute
Content-Type: application/json

[
  "替换为实际的API配置ID1",
  "替换为实际的API配置ID2"
]

### 14. 清理过期数据（删除30天前的数据）
DELETE http://localhost:5000/api/ApiResponseData/cleanup?olderThanDays=30
