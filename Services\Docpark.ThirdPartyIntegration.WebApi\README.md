# 第三方接口集成系统

## 项目概述

这是一个可配置的第三方RESTful API集成系统，支持多种授权方式、灵活的参数配置、定时调用和数据映射处理。

## 功能特性

### 1. 多种授权方式支持
- **基础认证（BasicAuth）**: 用户名密码认证
- **OAuth2**: 客户端凭证模式
- **API密钥**: 支持Header或Query参数
- **Bearer令牌**: 直接使用访问令牌
- **自定义授权**: 可扩展的授权方式

### 2. 灵活的参数配置
- **固定值**: 静态参数
- **当前时间**: 支持多种时间格式
- **时间戳**: 秒级或毫秒级
- **上次执行时间**: 增量数据获取
- **动态参数**: 可扩展的动态值生成

### 3. 数据存储和管理
- **MongoDB存储**: 原始响应数据和解析后的结构化数据
- **数据去重**: 基于内容哈希的重复检测
- **数据统计**: 执行成功率、数据量等统计信息
- **数据清理**: 自动清理过期数据

### 4. 执行日志和监控
- **详细日志**: 记录每次API调用的完整信息
- **执行状态**: 运行中、成功、失败、超时、取消
- **性能监控**: 执行时间、响应大小等指标
- **错误追踪**: 详细的错误信息和堆栈跟踪

## 快速开始

### 1. 环境要求
- .NET Core 3.1+
- MongoDB 4.0+

### 2. 配置数据库
在 `appsettings.json` 中配置MongoDB连接：

```json
{
  "ConnectionStrings": {
    "MongoDB": "mongodb://localhost:27017"
  },
  "MongoDB": {
    "DatabaseName": "ThirdPartyIntegration"
  }
}
```

### 3. 启动应用
```bash
cd Services/Docpark.ThirdPartyIntegration.WebApi
dotnet run
```

应用启动后，访问 `http://localhost:5000` 查看Swagger API文档。

## API使用示例

### 1. 创建授权配置

**BasicAuth示例：**
```json
{
  "name": "测试API基础认证",
  "type": 1,
  "parameters": {
    "username": "your_username",
    "password": "your_password"
  }
}
```

**OAuth2示例：**
```json
{
  "name": "OAuth2认证",
  "type": 2,
  "parameters": {
    "clientId": "your_client_id",
    "clientSecret": "your_client_secret",
    "tokenUrl": "https://api.example.com/oauth/token",
    "scope": "read write"
  }
}
```

### 2. 创建API配置

```json
{
  "name": "用户数据API",
  "description": "获取用户数据",
  "baseUrl": "https://api.example.com",
  "endpoint": "/api/users",
  "method": "GET",
  "authenticationConfigId": "授权配置ID",
  "parameters": [
    {
      "name": "page",
      "location": "query",
      "type": 1,
      "value": "1",
      "isRequired": true
    },
    {
      "name": "updated_since",
      "location": "query",
      "type": 4,
      "format": "yyyy-MM-ddTHH:mm:ssZ",
      "isRequired": false
    }
  ],
  "schedule": {
    "type": 1,
    "intervalMinutes": 30,
    "isEnabled": true
  }
}
```

### 3. 手动执行API

```bash
POST /api/ApiExecution/{apiConfigId}/execute
```

### 4. 查看执行结果

```bash
GET /api/ApiResponseData/by-api/{apiConfigId}
```

## 参数类型说明

| 类型 | 值 | 说明 | 示例 |
|------|----|----- |------|
| Static | 1 | 固定值 | "固定字符串" |
| CurrentTime | 2 | 当前时间 | format: "yyyy-MM-dd" |
| Timestamp | 3 | 时间戳 | format: "seconds" 或 "milliseconds" |
| LastExecutionTime | 4 | 上次执行时间 | format: "iso8601" |
| Dynamic | 99 | 动态值（可扩展） | 自定义逻辑 |

## 时间格式支持

- `iso8601`: 2023-12-01T10:30:00.000Z
- `date`: 2023-12-01
- `time`: 10:30:00
- `timestamp`: 1701423000
- `timestamp_ms`: 1701423000000
- 自定义格式: 使用.NET DateTime格式字符串

## 数据库集合

- `authentication_configs`: 授权配置
- `api_configurations`: API配置
- `api_execution_logs`: 执行日志
- `api_response_data`: 响应数据

## 扩展开发

### 添加新的授权方式
1. 在 `AuthenticationType` 枚举中添加新类型
2. 在 `AuthenticationService.AuthenticateAsync` 方法中添加处理逻辑

### 添加新的参数类型
1. 在 `ParameterType` 枚举中添加新类型
2. 在 `ParameterProcessingService.ProcessParameter` 方法中添加处理逻辑

## 注意事项

1. **安全性**: 敏感信息（如密码、密钥）建议加密存储
2. **性能**: 大量数据时建议配置适当的MongoDB索引
3. **监控**: 建议配置日志监控和告警
4. **备份**: 定期备份MongoDB数据

## 技术栈

- **后端**: ASP.NET Core 3.1
- **数据库**: MongoDB
- **HTTP客户端**: HttpClient
- **序列化**: Newtonsoft.Json
- **API文档**: Swagger/OpenAPI

## 第二阶段新增功能

### 1. 数据映射和转换功能 ✅

- **灵活的数据映射**: 支持JSON路径映射、字段重命名、数据类型转换
- **多种转换类型**: 字符串、数字、日期、布尔值、JSON对象等转换
- **数据验证**: 支持正则表达式、范围验证、格式验证等
- **映射预览**: 提供映射配置的预览和测试功能

**相关API端点:**
- `POST /api/DataMapping/validate` - 验证映射配置
- `POST /api/DataMapping/preview` - 生成映射预览
- `POST /api/DataMapping/test-transform` - 测试数据转换
- `PUT /api/DataMapping/api-config/{id}/mapping` - 更新API配置的映射设置

### 2. 响应数据处理功能 ✅

- **数据去重**: 基于哈希值、字段值或内容的智能去重
- **数据过滤**: 支持多种过滤条件和逻辑操作符
- **数据聚合**: 支持计数、求和、平均值、最大值、最小值等聚合操作
- **数据验证**: 多种验证规则和失败处理策略
- **批处理**: 支持大数据量的批量处理

**相关API端点:**
- `POST /api/ResponseProcessing/test-filter` - 测试数据过滤
- `POST /api/ResponseProcessing/test-validation` - 测试数据验证
- `POST /api/ResponseProcessing/test-aggregation` - 测试数据聚合
- `PUT /api/ResponseProcessing/api-config/{id}/processing` - 更新响应处理配置

### 3. 定时任务调度系统 ✅

- **基于Quartz.NET**: 企业级任务调度引擎
- **多种调度策略**: 间隔调度、每日调度、Cron表达式调度
- **任务管理**: 启动、停止、暂停、恢复、立即执行
- **执行历史**: 详细的任务执行记录和统计信息
- **错误处理**: 重试机制、超时处理、错失触发策略

**相关API端点:**
- `POST /api/Scheduler/start` - 启动调度器
- `POST /api/Scheduler/schedule/{apiConfigId}` - 调度API任务
- `GET /api/Scheduler/jobs` - 获取所有任务状态
- `POST /api/Scheduler/trigger/{jobKey}` - 立即执行任务
- `GET /api/Scheduler/statistics` - 获取调度器统计信息

### 4. 待开发功能

- **数据同步和增量更新**: 智能的数据同步机制
- **监控和告警系统**: API健康检查、性能监控、异常告警
- **Web管理界面**: 用户友好的管理界面
- **API网关和限流**: 请求限流、熔断器、负载均衡

## 技术栈更新

- **后端**: ASP.NET Core 3.1
- **数据库**: MongoDB
- **任务调度**: Quartz.NET 3.4.0
- **HTTP客户端**: HttpClient
- **序列化**: Newtonsoft.Json
- **API文档**: Swagger/OpenAPI

## 许可证

本项目采用MIT许可证。
