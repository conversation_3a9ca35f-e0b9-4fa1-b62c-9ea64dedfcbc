<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="3.1.9" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="3.1.9" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="3.1.9" />
    <PackageReference Include="MongoDB.Driver" Version="2.19.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Docpark.ThirdPartyIntegration.Domain\Docpark.ThirdPartyIntegration.Domain.csproj" />
    <ProjectReference Include="..\..\BuildingBlocks\DocPark.MongoDb\DocPark.MongoDb.csproj" />
    <ProjectReference Include="..\..\BuildingBlocks\Docpark.HttpClientExtension\Docpark.HttpClientExtension.csproj" />
  </ItemGroup>

</Project>
