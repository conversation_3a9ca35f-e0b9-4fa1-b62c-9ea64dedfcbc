using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Services.Interfaces;

namespace Docpark.ThirdPartyIntegration.WebApi.Controllers
{
    /// <summary>
    /// 授权配置控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class AuthenticationConfigController : ControllerBase
    {
        private readonly IAuthenticationConfigService _authConfigService;
        private readonly ILogger<AuthenticationConfigController> _logger;

        public AuthenticationConfigController(
            IAuthenticationConfigService authConfigService,
            ILogger<AuthenticationConfigController> logger)
        {
            _authConfigService = authConfigService;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有授权配置
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult<List<AuthenticationConfig>>> GetAll()
        {
            try
            {
                var configs = await _authConfigService.GetAllAsync();
                return Ok(configs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all authentication configs");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 根据ID获取授权配置
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<AuthenticationConfig>> GetById(string id)
        {
            try
            {
                var config = await _authConfigService.GetByIdAsync(id);
                if (config == null)
                {
                    return NotFound();
                }
                return Ok(config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting authentication config by id: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 创建授权配置
        /// </summary>
        /// <param name="config"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult<AuthenticationConfig>> Create([FromBody] AuthenticationConfig config)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var createdConfig = await _authConfigService.CreateAsync(config);
                return CreatedAtAction(nameof(GetById), new { id = createdConfig.Id }, createdConfig);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating authentication config");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 更新授权配置
        /// </summary>
        /// <param name="id"></param>
        /// <param name="config"></param>
        /// <returns></returns>
        [HttpPut("{id}")]
        public async Task<ActionResult<AuthenticationConfig>> Update(string id, [FromBody] AuthenticationConfig config)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                if (id != config.Id)
                {
                    return BadRequest("ID mismatch");
                }

                var existingConfig = await _authConfigService.GetByIdAsync(id);
                if (existingConfig == null)
                {
                    return NotFound();
                }

                var updatedConfig = await _authConfigService.UpdateAsync(config);
                return Ok(updatedConfig);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating authentication config: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 删除授权配置
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("{id}")]
        public async Task<ActionResult> Delete(string id)
        {
            try
            {
                var result = await _authConfigService.DeleteAsync(id);
                if (!result)
                {
                    return NotFound();
                }
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting authentication config: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 测试授权配置
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("{id}/test")]
        public async Task<ActionResult> TestAuthentication(string id)
        {
            try
            {
                var result = await _authConfigService.TestAuthenticationAsync(id);
                return Ok(new { success = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing authentication config: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// 启用/禁用授权配置
        /// </summary>
        /// <param name="id"></param>
        /// <param name="enabled"></param>
        /// <returns></returns>
        [HttpPatch("{id}/enabled")]
        public async Task<ActionResult> SetEnabled(string id, [FromBody] bool enabled)
        {
            try
            {
                var result = await _authConfigService.SetEnabledAsync(id, enabled);
                if (!result)
                {
                    return NotFound();
                }
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting authentication config enabled: {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
