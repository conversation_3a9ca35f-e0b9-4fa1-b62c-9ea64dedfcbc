using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Quartz;
using Quartz.Impl;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Services.Interfaces;
using Docpark.ThirdPartyIntegration.Services.Jobs;
using Docpark.ThirdPartyIntegration.Services.Infrastructure;
using MongoDB.Driver;

namespace Docpark.ThirdPartyIntegration.Services.Services
{
    /// <summary>
    /// 任务调度服务实现
    /// </summary>
    public class SchedulerService : ISchedulerService
    {
        private readonly IScheduler _scheduler;
        private readonly ILogger<SchedulerService> _logger;
        private readonly IMongoRepository _mongoRepository;

        public SchedulerService(IScheduler scheduler, ILogger<SchedulerService> logger, IMongoRepository mongoRepository)
        {
            _scheduler = scheduler;
            _logger = logger;
            _mongoRepository = mongoRepository;
        }

        /// <summary>
        /// 启动调度器
        /// </summary>
        public async Task StartAsync()
        {
            try
            {
                if (!_scheduler.IsStarted)
                {
                    await _scheduler.Start();
                    _logger.LogInformation("任务调度器已启动");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动任务调度器失败");
                throw;
            }
        }

        /// <summary>
        /// 停止调度器
        /// </summary>
        public async Task StopAsync()
        {
            try
            {
                if (_scheduler.IsStarted)
                {
                    await _scheduler.Shutdown(true);
                    _logger.LogInformation("任务调度器已停止");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止任务调度器失败");
                throw;
            }
        }

        /// <summary>
        /// 暂停调度器
        /// </summary>
        public async Task PauseAsync()
        {
            try
            {
                await _scheduler.PauseAll();
                _logger.LogInformation("任务调度器已暂停");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "暂停任务调度器失败");
                throw;
            }
        }

        /// <summary>
        /// 恢复调度器
        /// </summary>
        public async Task ResumeAsync()
        {
            try
            {
                await _scheduler.ResumeAll();
                _logger.LogInformation("任务调度器已恢复");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "恢复任务调度器失败");
                throw;
            }
        }

        /// <summary>
        /// 调度API任务
        /// </summary>
        public async Task<string> ScheduleApiJobAsync(string apiConfigId, ScheduleConfig scheduleConfig)
        {
            try
            {
                var jobKey = new JobKey($"api-job-{apiConfigId}", scheduleConfig.JobGroup);
                var triggerKey = new TriggerKey($"api-trigger-{apiConfigId}", scheduleConfig.JobGroup);

                // 如果任务已存在，先删除
                if (await _scheduler.CheckExists(jobKey))
                {
                    await _scheduler.DeleteJob(jobKey);
                    _logger.LogInformation("删除已存在的任务，任务键: {JobKey}", jobKey);
                }

                // 创建任务
                var jobBuilder = JobBuilder.Create<ApiExecutionJob>()
                    .WithIdentity(jobKey)
                    .WithDescription(scheduleConfig.Description ?? $"API任务: {apiConfigId}")
                    .UsingJobData("ApiConfigId", apiConfigId)
                    .UsingJobData("RetryCount", scheduleConfig.RetryCount)
                    .UsingJobData("TimeoutMinutes", scheduleConfig.TimeoutMinutes)
                    .UsingJobData("MaxExecutionCount", scheduleConfig.MaxExecutionCount)
                    .UsingJobData("ExecutionCount", 0)
                    .UsingJobData("SuccessCount", 0)
                    .UsingJobData("FailureCount", 0);

                // 设置并发执行策略
                if (!scheduleConfig.AllowConcurrentExecution)
                {
                    jobBuilder = jobBuilder.StoreDurably(false);
                }

                var job = jobBuilder.Build();

                // 创建触发器
                ITrigger trigger = CreateTrigger(triggerKey, scheduleConfig);

                // 调度任务
                await _scheduler.ScheduleJob(job, trigger);

                var jobKeyString = jobKey.ToString();
                _logger.LogInformation("成功调度API任务，任务键: {JobKey}, API配置ID: {ApiConfigId}", jobKeyString, apiConfigId);

                return jobKeyString;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "调度API任务失败，API配置ID: {ApiConfigId}", apiConfigId);
                throw;
            }
        }

        /// <summary>
        /// 取消调度任务
        /// </summary>
        public async Task<bool> UnscheduleJobAsync(string jobKey)
        {
            try
            {
                var key = ParseJobKey(jobKey);
                var result = await _scheduler.DeleteJob(key);
                
                if (result)
                {
                    _logger.LogInformation("成功取消调度任务，任务键: {JobKey}", jobKey);
                }
                else
                {
                    _logger.LogWarning("取消调度任务失败，任务不存在，任务键: {JobKey}", jobKey);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取消调度任务失败，任务键: {JobKey}", jobKey);
                return false;
            }
        }

        /// <summary>
        /// 暂停任务
        /// </summary>
        public async Task<bool> PauseJobAsync(string jobKey)
        {
            try
            {
                var key = ParseJobKey(jobKey);
                await _scheduler.PauseJob(key);
                _logger.LogInformation("成功暂停任务，任务键: {JobKey}", jobKey);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "暂停任务失败，任务键: {JobKey}", jobKey);
                return false;
            }
        }

        /// <summary>
        /// 恢复任务
        /// </summary>
        public async Task<bool> ResumeJobAsync(string jobKey)
        {
            try
            {
                var key = ParseJobKey(jobKey);
                await _scheduler.ResumeJob(key);
                _logger.LogInformation("成功恢复任务，任务键: {JobKey}", jobKey);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "恢复任务失败，任务键: {JobKey}", jobKey);
                return false;
            }
        }

        /// <summary>
        /// 立即执行任务
        /// </summary>
        public async Task<bool> TriggerJobAsync(string jobKey)
        {
            try
            {
                var key = ParseJobKey(jobKey);
                await _scheduler.TriggerJob(key);
                _logger.LogInformation("成功触发任务执行，任务键: {JobKey}", jobKey);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "触发任务执行失败，任务键: {JobKey}", jobKey);
                return false;
            }
        }

        /// <summary>
        /// 更新任务调度
        /// </summary>
        public async Task<bool> RescheduleJobAsync(string jobKey, ScheduleConfig scheduleConfig)
        {
            try
            {
                var key = ParseJobKey(jobKey);
                var jobDetail = await _scheduler.GetJobDetail(key);
                
                if (jobDetail == null)
                {
                    _logger.LogWarning("重新调度任务失败，任务不存在，任务键: {JobKey}", jobKey);
                    return false;
                }

                // 获取API配置ID
                var apiConfigId = jobDetail.JobDataMap.GetString("ApiConfigId");
                
                // 删除旧任务并创建新任务
                await _scheduler.DeleteJob(key);
                await ScheduleApiJobAsync(apiConfigId, scheduleConfig);

                _logger.LogInformation("成功重新调度任务，任务键: {JobKey}", jobKey);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重新调度任务失败，任务键: {JobKey}", jobKey);
                return false;
            }
        }

        /// <summary>
        /// 获取任务状态
        /// </summary>
        public async Task<JobStatus> GetJobStatusAsync(string jobKey)
        {
            try
            {
                var key = ParseJobKey(jobKey);
                var jobDetail = await _scheduler.GetJobDetail(key);
                
                if (jobDetail == null)
                {
                    return null;
                }

                var triggers = await _scheduler.GetTriggersOfJob(key);
                var trigger = triggers.FirstOrDefault();
                var triggerState = trigger != null ? await _scheduler.GetTriggerState(trigger.Key) : Quartz.TriggerState.None;

                var jobStatus = new JobStatus
                {
                    JobKey = jobKey,
                    JobName = key.Name,
                    JobGroup = key.Group,
                    Description = jobDetail.Description,
                    State = ConvertJobState(await _scheduler.GetJobDetail(key) != null),
                    TriggerState = ConvertTriggerState(triggerState),
                    ApiConfigId = jobDetail.JobDataMap.GetString("ApiConfigId"),
                    ExecutionCount = jobDetail.JobDataMap.GetIntValue("ExecutionCount"),
                    MaxExecutionCount = jobDetail.JobDataMap.GetIntValue("MaxExecutionCount"),
                    IsRunning = await _scheduler.GetCurrentlyExecutingJobs().ContinueWith(t => 
                        t.Result.Any(j => j.JobDetail.Key.Equals(key)))
                };

                if (trigger != null)
                {
                    jobStatus.NextFireTime = trigger.GetNextFireTimeUtc()?.DateTime;
                    jobStatus.PreviousFireTime = trigger.GetPreviousFireTimeUtc()?.DateTime;
                    jobStatus.StartTime = trigger.StartTimeUtc.DateTime;
                    jobStatus.EndTime = trigger.EndTimeUtc?.DateTime;
                }

                return jobStatus;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务状态失败，任务键: {JobKey}", jobKey);
                return null;
            }
        }

        /// <summary>
        /// 获取所有任务状态
        /// </summary>
        public async Task<List<JobStatus>> GetAllJobStatusAsync()
        {
            try
            {
                var jobStatuses = new List<JobStatus>();
                var jobGroups = await _scheduler.GetJobGroupNames();

                foreach (var group in jobGroups)
                {
                    var jobKeys = await _scheduler.GetJobKeys(GroupMatcher<JobKey>.GroupEquals(group));
                    
                    foreach (var jobKey in jobKeys)
                    {
                        var status = await GetJobStatusAsync(jobKey.ToString());
                        if (status != null)
                        {
                            jobStatuses.Add(status);
                        }
                    }
                }

                return jobStatuses;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有任务状态失败");
                return new List<JobStatus>();
            }
        }

        /// <summary>
        /// 获取任务执行历史
        /// </summary>
        public async Task<List<JobExecutionHistory>> GetJobExecutionHistoryAsync(string jobKey, int limit = 50)
        {
            try
            {
                var collection = _mongoRepository.GetCollection<JobExecutionHistory>("job_execution_history");
                var filter = Builders<JobExecutionHistory>.Filter.Eq(x => x.JobKey, jobKey);
                var sort = Builders<JobExecutionHistory>.Sort.Descending(x => x.StartTime);

                var histories = await collection.Find(filter)
                    .Sort(sort)
                    .Limit(limit)
                    .ToListAsync();

                return histories;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务执行历史失败，任务键: {JobKey}", jobKey);
                return new List<JobExecutionHistory>();
            }
        }

        /// <summary>
        /// 清理过期的执行历史
        /// </summary>
        public async Task<int> CleanupExecutionHistoryAsync(int olderThanDays = 30)
        {
            try
            {
                var collection = _mongoRepository.GetCollection<JobExecutionHistory>("job_execution_history");
                var cutoffDate = DateTime.UtcNow.AddDays(-olderThanDays);
                var filter = Builders<JobExecutionHistory>.Filter.Lt(x => x.StartTime, cutoffDate);

                var result = await collection.DeleteManyAsync(filter);
                var deletedCount = (int)result.DeletedCount;

                _logger.LogInformation("清理过期执行历史完成，删除记录数: {DeletedCount}", deletedCount);
                return deletedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理过期执行历史失败");
                return 0;
            }
        }

        /// <summary>
        /// 获取调度器统计信息
        /// </summary>
        public async Task<SchedulerStatistics> GetSchedulerStatisticsAsync()
        {
            try
            {
                var metaData = await _scheduler.GetMetaData();
                var allJobs = await GetAllJobStatusAsync();
                var today = DateTime.UtcNow.Date;

                // 获取今日执行统计
                var collection = _mongoRepository.GetCollection<JobExecutionHistory>("job_execution_history");
                var todayFilter = Builders<JobExecutionHistory>.Filter.Gte(x => x.StartTime, today);
                var todayExecutions = await collection.CountDocumentsAsync(todayFilter);
                
                var todaySuccessFilter = Builders<JobExecutionHistory>.Filter.And(
                    todayFilter,
                    Builders<JobExecutionHistory>.Filter.Eq(x => x.Status, JobExecutionStatus.Success)
                );
                var todaySuccesses = await collection.CountDocumentsAsync(todaySuccessFilter);

                var todayFailureFilter = Builders<JobExecutionHistory>.Filter.And(
                    todayFilter,
                    Builders<JobExecutionHistory>.Filter.Eq(x => x.Status, JobExecutionStatus.Failed)
                );
                var todayFailures = await collection.CountDocumentsAsync(todayFailureFilter);

                var statistics = new SchedulerStatistics
                {
                    SchedulerName = metaData.SchedulerName,
                    SchedulerState = metaData.SchedulerRemotelyStarted ? "远程启动" : "本地启动",
                    StartTime = metaData.RunningSince?.DateTime,
                    RunTime = metaData.RunningSince.HasValue ? DateTime.UtcNow - metaData.RunningSince.Value.DateTime : null,
                    TotalJobs = allJobs.Count,
                    RunningJobs = allJobs.Count(j => j.IsRunning),
                    PausedJobs = allJobs.Count(j => j.State == JobState.Paused),
                    CompletedJobs = allJobs.Count(j => j.State == JobState.Complete),
                    FailedJobs = allJobs.Count(j => j.State == JobState.Error),
                    TotalTriggers = metaData.NumberOfTriggersFired,
                    TodayExecutions = (int)todayExecutions,
                    TodaySuccesses = (int)todaySuccesses,
                    TodayFailures = (int)todayFailures
                };

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取调度器统计信息失败");
                return new SchedulerStatistics();
            }
        }

        /// <summary>
        /// 验证Cron表达式
        /// </summary>
        public async Task<CronValidationResult> ValidateCronExpressionAsync(string cronExpression)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(cronExpression))
                {
                    return new CronValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = "Cron表达式不能为空"
                    };
                }

                var cronScheduleBuilder = CronScheduleBuilder.CronSchedule(cronExpression);
                var trigger = TriggerBuilder.Create()
                    .WithSchedule(cronScheduleBuilder)
                    .Build();

                var nextFireTime = trigger.GetNextFireTimeUtc();

                return new CronValidationResult
                {
                    IsValid = true,
                    Description = $"Cron表达式有效",
                    NextExecutionTime = nextFireTime?.DateTime
                };
            }
            catch (Exception ex)
            {
                return new CronValidationResult
                {
                    IsValid = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 获取Cron表达式的下几次执行时间
        /// </summary>
        public async Task<List<DateTime>> GetCronNextExecutionTimesAsync(string cronExpression, int count = 5)
        {
            try
            {
                var times = new List<DateTime>();
                
                if (string.IsNullOrWhiteSpace(cronExpression))
                {
                    return times;
                }

                var cronScheduleBuilder = CronScheduleBuilder.CronSchedule(cronExpression);
                var trigger = TriggerBuilder.Create()
                    .WithSchedule(cronScheduleBuilder)
                    .StartNow()
                    .Build();

                var currentTime = DateTimeOffset.UtcNow;
                
                for (int i = 0; i < count; i++)
                {
                    var nextTime = trigger.GetFireTimeAfter(currentTime);
                    if (nextTime.HasValue)
                    {
                        times.Add(nextTime.Value.DateTime);
                        currentTime = nextTime.Value;
                    }
                    else
                    {
                        break;
                    }
                }

                return times;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取Cron表达式执行时间失败，表达式: {CronExpression}", cronExpression);
                return new List<DateTime>();
            }
        }

        #region 私有方法

        private ITrigger CreateTrigger(TriggerKey triggerKey, ScheduleConfig scheduleConfig)
        {
            var triggerBuilder = TriggerBuilder.Create()
                .WithIdentity(triggerKey)
                .WithPriority(scheduleConfig.Priority);

            // 设置开始和结束时间
            if (scheduleConfig.StartTime.HasValue)
            {
                triggerBuilder = triggerBuilder.StartAt(scheduleConfig.StartTime.Value);
            }
            else
            {
                triggerBuilder = triggerBuilder.StartNow();
            }

            if (scheduleConfig.EndTime.HasValue)
            {
                triggerBuilder = triggerBuilder.EndAt(scheduleConfig.EndTime.Value);
            }

            // 根据调度类型创建不同的触发器
            switch (scheduleConfig.Type)
            {
                case Domain.Enums.ScheduleType.Interval:
                    var intervalBuilder = SimpleScheduleBuilder.Create()
                        .WithIntervalInMinutes(scheduleConfig.IntervalMinutes);

                    if (scheduleConfig.MaxExecutionCount > 0)
                    {
                        intervalBuilder = intervalBuilder.WithRepeatCount(scheduleConfig.MaxExecutionCount - 1);
                    }
                    else
                    {
                        intervalBuilder = intervalBuilder.RepeatForever();
                    }

                    // 设置错失触发策略
                    switch (scheduleConfig.MisfireInstruction)
                    {
                        case MisfireInstruction.IgnoreMisfires:
                            intervalBuilder = intervalBuilder.WithMisfireHandlingInstructionIgnoreMisfires();
                            break;
                        case MisfireInstruction.FireOnceNow:
                            intervalBuilder = intervalBuilder.WithMisfireHandlingInstructionFireNow();
                            break;
                        case MisfireInstruction.DoNothing:
                            intervalBuilder = intervalBuilder.WithMisfireHandlingInstructionNextWithExistingCount();
                            break;
                    }

                    triggerBuilder = triggerBuilder.WithSchedule(intervalBuilder);
                    break;

                case Domain.Enums.ScheduleType.Daily:
                    var dailyTime = scheduleConfig.DailyExecutionTime ?? TimeSpan.FromHours(9); // 默认上午9点
                    var dailyBuilder = CronScheduleBuilder.DailyAtHourAndMinute(dailyTime.Hours, dailyTime.Minutes);
                    triggerBuilder = triggerBuilder.WithSchedule(dailyBuilder);
                    break;

                case Domain.Enums.ScheduleType.Cron:
                    if (!string.IsNullOrEmpty(scheduleConfig.CronExpression))
                    {
                        var cronBuilder = CronScheduleBuilder.CronSchedule(scheduleConfig.CronExpression);
                        
                        // 设置时区
                        if (!string.IsNullOrEmpty(scheduleConfig.TimeZone))
                        {
                            try
                            {
                                var timeZone = TimeZoneInfo.FindSystemTimeZoneById(scheduleConfig.TimeZone);
                                cronBuilder = cronBuilder.InTimeZone(timeZone);
                            }
                            catch
                            {
                                _logger.LogWarning("无效的时区设置: {TimeZone}，使用UTC时区", scheduleConfig.TimeZone);
                            }
                        }

                        triggerBuilder = triggerBuilder.WithSchedule(cronBuilder);
                    }
                    else
                    {
                        throw new ArgumentException("Cron调度类型必须提供Cron表达式");
                    }
                    break;

                default:
                    throw new ArgumentException($"不支持的调度类型: {scheduleConfig.Type}");
            }

            return triggerBuilder.Build();
        }

        private JobKey ParseJobKey(string jobKeyString)
        {
            var parts = jobKeyString.Split('.');
            if (parts.Length == 2)
            {
                return new JobKey(parts[1], parts[0]);
            }
            else
            {
                return new JobKey(jobKeyString, "DEFAULT");
            }
        }

        private JobState ConvertJobState(bool exists)
        {
            return exists ? JobState.Normal : JobState.None;
        }

        private TriggerState ConvertTriggerState(Quartz.TriggerState quartzState)
        {
            return quartzState switch
            {
                Quartz.TriggerState.Normal => TriggerState.Normal,
                Quartz.TriggerState.Paused => TriggerState.Paused,
                Quartz.TriggerState.Complete => TriggerState.Complete,
                Quartz.TriggerState.Error => TriggerState.Error,
                Quartz.TriggerState.Blocked => TriggerState.Blocked,
                Quartz.TriggerState.None => TriggerState.None,
                _ => TriggerState.None
            };
        }

        #endregion
    }
}
