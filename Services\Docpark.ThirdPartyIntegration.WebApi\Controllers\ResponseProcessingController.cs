using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Services.Interfaces;

namespace Docpark.ThirdPartyIntegration.WebApi.Controllers
{
    /// <summary>
    /// 响应处理配置控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class ResponseProcessingController : ControllerBase
    {
        private readonly IResponseProcessingService _responseProcessingService;
        private readonly IApiConfigurationService _apiConfigurationService;
        private readonly ILogger<ResponseProcessingController> _logger;

        public ResponseProcessingController(
            IResponseProcessingService responseProcessingService,
            IApiConfigurationService apiConfigurationService,
            ILogger<ResponseProcessingController> logger)
        {
            _responseProcessingService = responseProcessingService;
            _apiConfigurationService = apiConfigurationService;
            _logger = logger;
        }

        /// <summary>
        /// 更新API配置的响应处理设置
        /// </summary>
        /// <param name="apiConfigId">API配置ID</param>
        /// <param name="processingConfig">响应处理配置</param>
        /// <returns>更新结果</returns>
        [HttpPut("api-config/{apiConfigId}/processing")]
        public async Task<IActionResult> UpdateApiProcessingConfig(string apiConfigId, [FromBody] ResponseProcessingConfig processingConfig)
        {
            try
            {
                if (string.IsNullOrEmpty(apiConfigId))
                {
                    return BadRequest(new { message = "API配置ID不能为空" });
                }

                // 获取API配置
                var apiConfig = await _apiConfigurationService.GetByIdAsync(apiConfigId);
                if (apiConfig == null)
                {
                    return NotFound(new { message = "API配置不存在" });
                }

                // 更新响应处理配置
                apiConfig.ResponseProcessing = processingConfig;
                apiConfig.UpdatedAt = DateTime.UtcNow;

                await _apiConfigurationService.UpdateAsync(apiConfig);

                _logger.LogInformation("API配置 {ApiConfigId} 的响应处理设置已更新", apiConfigId);

                return Ok(new { 
                    message = "响应处理配置更新成功",
                    apiConfigId = apiConfigId
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新API配置的响应处理设置失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "更新响应处理配置失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取API配置的响应处理设置
        /// </summary>
        /// <param name="apiConfigId">API配置ID</param>
        /// <returns>响应处理配置</returns>
        [HttpGet("api-config/{apiConfigId}/processing")]
        public async Task<IActionResult> GetApiProcessingConfig(string apiConfigId)
        {
            try
            {
                if (string.IsNullOrEmpty(apiConfigId))
                {
                    return BadRequest(new { message = "API配置ID不能为空" });
                }

                var apiConfig = await _apiConfigurationService.GetByIdAsync(apiConfigId);
                if (apiConfig == null)
                {
                    return NotFound(new { message = "API配置不存在" });
                }

                return Ok(apiConfig.ResponseProcessing ?? new ResponseProcessingConfig());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取API配置的响应处理设置失败，API配置ID: {ApiConfigId}", apiConfigId);
                return StatusCode(500, new { message = "获取响应处理配置失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 测试数据过滤
        /// </summary>
        /// <param name="request">过滤测试请求</param>
        /// <returns>过滤结果</returns>
        [HttpPost("test-filter")]
        public async Task<IActionResult> TestDataFilter([FromBody] FilterTestRequest request)
        {
            try
            {
                if (request?.TestData == null || request?.FilterConfig == null)
                {
                    return BadRequest(new { message = "测试数据和过滤配置不能为空" });
                }

                var result = await _responseProcessingService.FilterDataAsync(request.TestData, request.FilterConfig);

                return Ok(new { 
                    originalCount = request.TestData.Count,
                    filteredCount = result.Count,
                    filteredData = result
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试数据过滤失败");
                return StatusCode(500, new { message = "测试数据过滤失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 测试数据验证
        /// </summary>
        /// <param name="request">验证测试请求</param>
        /// <returns>验证结果</returns>
        [HttpPost("test-validation")]
        public async Task<IActionResult> TestDataValidation([FromBody] ValidationTestRequest request)
        {
            try
            {
                if (request?.TestData == null || request?.ValidationConfig == null)
                {
                    return BadRequest(new { message = "测试数据和验证配置不能为空" });
                }

                var result = await _responseProcessingService.ValidateDataAsync(request.TestData, request.ValidationConfig);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试数据验证失败");
                return StatusCode(500, new { message = "测试数据验证失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 测试数据聚合
        /// </summary>
        /// <param name="request">聚合测试请求</param>
        /// <returns>聚合结果</returns>
        [HttpPost("test-aggregation")]
        public async Task<IActionResult> TestDataAggregation([FromBody] AggregationTestRequest request)
        {
            try
            {
                if (request?.TestData == null || request?.AggregationConfig == null)
                {
                    return BadRequest(new { message = "测试数据和聚合配置不能为空" });
                }

                var result = await _responseProcessingService.AggregateDataAsync(request.TestData, request.AggregationConfig);

                return Ok(new { 
                    originalCount = request.TestData.Count,
                    aggregatedCount = result.Count,
                    aggregatedData = result
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试数据聚合失败");
                return StatusCode(500, new { message = "测试数据聚合失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 计算数据哈希值
        /// </summary>
        /// <param name="request">哈希计算请求</param>
        /// <returns>哈希值</returns>
        [HttpPost("compute-hash")]
        public async Task<IActionResult> ComputeDataHash([FromBody] HashComputeRequest request)
        {
            try
            {
                if (request?.Data == null)
                {
                    return BadRequest(new { message = "数据不能为空" });
                }

                var hash = await _responseProcessingService.ComputeDataHashAsync(request.Data, request.Algorithm ?? "SHA256");

                return Ok(new { 
                    data = request.Data,
                    algorithm = request.Algorithm ?? "SHA256",
                    hash = hash
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算数据哈希值失败");
                return StatusCode(500, new { message = "计算数据哈希值失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取支持的过滤操作符
        /// </summary>
        /// <returns>操作符列表</returns>
        [HttpGet("filter-operators")]
        public IActionResult GetFilterOperators()
        {
            try
            {
                var operators = Enum.GetValues<FilterOperator>()
                    .Select(op => new
                    {
                        value = (int)op,
                        name = op.ToString(),
                        description = GetFilterOperatorDescription(op)
                    })
                    .ToList();

                return Ok(operators);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取过滤操作符失败");
                return StatusCode(500, new { message = "获取过滤操作符失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取支持的聚合类型
        /// </summary>
        /// <returns>聚合类型列表</returns>
        [HttpGet("aggregation-types")]
        public IActionResult GetAggregationTypes()
        {
            try
            {
                var types = Enum.GetValues<AggregationType>()
                    .Select(t => new
                    {
                        value = (int)t,
                        name = t.ToString(),
                        description = GetAggregationTypeDescription(t)
                    })
                    .ToList();

                return Ok(types);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取聚合类型失败");
                return StatusCode(500, new { message = "获取聚合类型失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取支持的验证类型
        /// </summary>
        /// <returns>验证类型列表</returns>
        [HttpGet("validation-types")]
        public IActionResult GetValidationTypes()
        {
            try
            {
                var types = Enum.GetValues<ValidationType>()
                    .Select(t => new
                    {
                        value = (int)t,
                        name = t.ToString(),
                        description = GetValidationTypeDescription(t)
                    })
                    .ToList();

                return Ok(types);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取验证类型失败");
                return StatusCode(500, new { message = "获取验证类型失败", error = ex.Message });
            }
        }

        #region 私有方法

        private string GetFilterOperatorDescription(FilterOperator op)
        {
            return op switch
            {
                FilterOperator.Equals => "等于",
                FilterOperator.NotEquals => "不等于",
                FilterOperator.GreaterThan => "大于",
                FilterOperator.GreaterThanOrEqual => "大于等于",
                FilterOperator.LessThan => "小于",
                FilterOperator.LessThanOrEqual => "小于等于",
                FilterOperator.Contains => "包含",
                FilterOperator.NotContains => "不包含",
                FilterOperator.StartsWith => "开始于",
                FilterOperator.EndsWith => "结束于",
                FilterOperator.Regex => "正则匹配",
                FilterOperator.In => "在列表中",
                FilterOperator.NotIn => "不在列表中",
                _ => "未知操作符"
            };
        }

        private string GetAggregationTypeDescription(AggregationType type)
        {
            return type switch
            {
                AggregationType.Count => "计数",
                AggregationType.Sum => "求和",
                AggregationType.Average => "平均值",
                AggregationType.Max => "最大值",
                AggregationType.Min => "最小值",
                AggregationType.First => "第一个值",
                AggregationType.Last => "最后一个值",
                _ => "未知聚合类型"
            };
        }

        private string GetValidationTypeDescription(ValidationType type)
        {
            return type switch
            {
                ValidationType.Regex => "正则表达式",
                ValidationType.Range => "数值范围",
                ValidationType.Length => "字符串长度",
                ValidationType.Email => "邮箱格式",
                ValidationType.Url => "URL格式",
                ValidationType.Date => "日期格式",
                ValidationType.Custom => "自定义验证",
                _ => "未知验证类型"
            };
        }

        #endregion
    }

    #region 请求模型

    /// <summary>
    /// 过滤测试请求
    /// </summary>
    public class FilterTestRequest
    {
        public List<object> TestData { get; set; }
        public DataFilterConfig FilterConfig { get; set; }
    }

    /// <summary>
    /// 验证测试请求
    /// </summary>
    public class ValidationTestRequest
    {
        public List<object> TestData { get; set; }
        public DataValidationConfig ValidationConfig { get; set; }
    }

    /// <summary>
    /// 聚合测试请求
    /// </summary>
    public class AggregationTestRequest
    {
        public List<object> TestData { get; set; }
        public DataAggregationConfig AggregationConfig { get; set; }
    }

    /// <summary>
    /// 哈希计算请求
    /// </summary>
    public class HashComputeRequest
    {
        public object Data { get; set; }
        public string Algorithm { get; set; }
    }

    #endregion
}
