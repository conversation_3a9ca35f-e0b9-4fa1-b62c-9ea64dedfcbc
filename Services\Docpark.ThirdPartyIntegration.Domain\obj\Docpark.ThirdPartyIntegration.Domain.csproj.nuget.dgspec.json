{"format": 1, "restore": {"E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\Services\\Docpark.ThirdPartyIntegration.Domain\\Docpark.ThirdPartyIntegration.Domain.csproj": {}}, "projects": {"E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\Services\\Docpark.ThirdPartyIntegration.Domain\\Docpark.ThirdPartyIntegration.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\Services\\Docpark.ThirdPartyIntegration.Domain\\Docpark.ThirdPartyIntegration.Domain.csproj", "projectName": "Docpark.ThirdPartyIntegration.Domain", "projectPath": "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\Services\\Docpark.ThirdPartyIntegration.Domain\\Docpark.ThirdPartyIntegration.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Work Folder\\Projects\\Code\\Docpark\\Docpark.ExtendedFeature\\Services\\Docpark.ThirdPartyIntegration.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"MongoDB.Driver": {"target": "Package", "version": "[2.19.0, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}}