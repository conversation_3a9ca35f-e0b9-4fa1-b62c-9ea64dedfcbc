using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Docpark.ThirdPartyIntegration.Domain.Entities;
using Docpark.ThirdPartyIntegration.Domain.Enums;
using Docpark.ThirdPartyIntegration.Services.Interfaces;
using Docpark.ThirdPartyIntegration.Services.Models;

namespace Docpark.ThirdPartyIntegration.Services.Services
{
    /// <summary>
    /// 授权服务实现
    /// </summary>
    public class AuthenticationService : IAuthenticationService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<AuthenticationService> _logger;

        public AuthenticationService(HttpClient httpClient, ILogger<AuthenticationService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
        }

        public async Task<AuthenticationResult> AuthenticateAsync(AuthenticationConfig config)
        {
            try
            {
                switch (config.Type)
                {
                    case AuthenticationType.BasicAuth:
                        return await HandleBasicAuthAsync(config);
                    case AuthenticationType.OAuth2:
                        return await HandleOAuth2Async(config);
                    case AuthenticationType.ApiKey:
                        return HandleApiKey(config);
                    case AuthenticationType.BearerToken:
                        return HandleBearerToken(config);
                    case AuthenticationType.Custom:
                        return await HandleCustomAuthAsync(config);
                    default:
                        return AuthenticationResult.Failure($"Unsupported authentication type: {config.Type}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during authentication for config: {ConfigId}", config.Id);
                return AuthenticationResult.Failure($"Authentication failed: {ex.Message}");
            }
        }

        private async Task<AuthenticationResult> HandleBasicAuthAsync(AuthenticationConfig config)
        {
            if (!config.Parameters.TryGetValue("username", out var username) ||
                !config.Parameters.TryGetValue("password", out var password))
            {
                return AuthenticationResult.Failure("Username and password are required for Basic Auth");
            }

            var credentials = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{username}:{password}"));
            var token = $"Basic {credentials}";

            return AuthenticationResult.Success(token);
        }

        private async Task<AuthenticationResult> HandleOAuth2Async(AuthenticationConfig config)
        {
            if (!config.Parameters.TryGetValue("clientId", out var clientId) ||
                !config.Parameters.TryGetValue("clientSecret", out var clientSecret) ||
                !config.Parameters.TryGetValue("tokenUrl", out var tokenUrl))
            {
                return AuthenticationResult.Failure("ClientId, ClientSecret, and TokenUrl are required for OAuth2");
            }

            var requestData = new List<KeyValuePair<string, string>>
            {
                new KeyValuePair<string, string>("grant_type", "client_credentials"),
                new KeyValuePair<string, string>("client_id", clientId),
                new KeyValuePair<string, string>("client_secret", clientSecret)
            };

            if (config.Parameters.TryGetValue("scope", out var scope))
            {
                requestData.Add(new KeyValuePair<string, string>("scope", scope));
            }

            var content = new FormUrlEncodedContent(requestData);
            var response = await _httpClient.PostAsync(tokenUrl, content);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var tokenResponse = JsonConvert.DeserializeObject<OAuth2TokenResponse>(responseContent);

                var expiresAt = tokenResponse.ExpiresIn.HasValue
                    ? DateTime.UtcNow.AddSeconds(tokenResponse.ExpiresIn.Value)
                    : (DateTime?)null;

                return AuthenticationResult.Success(tokenResponse.AccessToken, tokenResponse.RefreshToken, expiresAt);
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                return AuthenticationResult.Failure($"OAuth2 authentication failed: {response.StatusCode} - {errorContent}");
            }
        }

        private AuthenticationResult HandleApiKey(AuthenticationConfig config)
        {
            if (!config.Parameters.TryGetValue("keyName", out var keyName) ||
                !config.Parameters.TryGetValue("keyValue", out var keyValue))
            {
                return AuthenticationResult.Failure("KeyName and KeyValue are required for API Key authentication");
            }

            var result = AuthenticationResult.Success(keyValue);
            result.AdditionalData["keyName"] = keyName;
            result.AdditionalData["location"] = config.Parameters.GetValueOrDefault("location", "header");

            return result;
        }

        private AuthenticationResult HandleBearerToken(AuthenticationConfig config)
        {
            if (!config.Parameters.TryGetValue("token", out var token))
            {
                return AuthenticationResult.Failure("Token is required for Bearer Token authentication");
            }

            return AuthenticationResult.Success(token);
        }

        private async Task<AuthenticationResult> HandleCustomAuthAsync(AuthenticationConfig config)
        {
            // 自定义授权逻辑，可以根据需要扩展
            _logger.LogWarning("Custom authentication not implemented for config: {ConfigId}", config.Id);
            return AuthenticationResult.Failure("Custom authentication not implemented");
        }

        public async Task<bool> ValidateTokenAsync(string token)
        {
            // 实现令牌验证逻辑
            return !string.IsNullOrEmpty(token);
        }

        public async Task<string> RefreshTokenAsync(string refreshToken)
        {
            // 实现令牌刷新逻辑
            throw new NotImplementedException("Token refresh not implemented");
        }

        public async Task<string> GetAuthorizationHeaderAsync(AuthenticationConfig config)
        {
            var result = await AuthenticateAsync(config);
            if (!result.IsSuccess)
            {
                throw new InvalidOperationException($"Authentication failed: {result.ErrorMessage}");
            }

            switch (config.Type)
            {
                case AuthenticationType.BasicAuth:
                    return result.AccessToken; // Already includes "Basic " prefix
                case AuthenticationType.OAuth2:
                case AuthenticationType.BearerToken:
                    return $"Bearer {result.AccessToken}";
                case AuthenticationType.ApiKey:
                    // API Key handling depends on location (header/query)
                    return result.AccessToken;
                default:
                    return result.AccessToken;
            }
        }
    }

    /// <summary>
    /// OAuth2令牌响应模型
    /// </summary>
    public class OAuth2TokenResponse
    {
        [JsonProperty("access_token")]
        public string AccessToken { get; set; }

        [JsonProperty("refresh_token")]
        public string RefreshToken { get; set; }

        [JsonProperty("token_type")]
        public string TokenType { get; set; }

        [JsonProperty("expires_in")]
        public int? ExpiresIn { get; set; }

        [JsonProperty("scope")]
        public string Scope { get; set; }
    }
}
