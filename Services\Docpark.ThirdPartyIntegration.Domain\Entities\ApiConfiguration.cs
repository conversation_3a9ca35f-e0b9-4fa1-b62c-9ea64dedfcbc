using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Net.Http;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Docpark.ThirdPartyIntegration.Domain.Entities
{
    /// <summary>
    /// API配置实体
    /// </summary>
    public class ApiConfiguration
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        /// <summary>
        /// 配置名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        /// <summary>
        /// 配置描述
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// 基础URL
        /// </summary>
        [Required]
        [StringLength(500)]
        public string BaseUrl { get; set; }

        /// <summary>
        /// 接口端点
        /// </summary>
        [Required]
        [StringLength(500)]
        public string Endpoint { get; set; }

        /// <summary>
        /// HTTP方法
        /// </summary>
        [Required]
        [StringLength(10)]
        public string Method { get; set; } = "GET";

        /// <summary>
        /// 授权配置ID
        /// </summary>
        public string AuthenticationConfigId { get; set; }

        /// <summary>
        /// API参数列表
        /// </summary>
        public List<ApiParameter> Parameters { get; set; } = new List<ApiParameter>();

        /// <summary>
        /// 调度配置
        /// </summary>
        public ScheduleConfig Schedule { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [StringLength(100)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        [StringLength(100)]
        public string UpdatedBy { get; set; }

        /// <summary>
        /// 数据映射配置
        /// </summary>
        public DataMappingConfig DataMapping { get; set; }

        /// <summary>
        /// 响应数据处理配置
        /// </summary>
        public ResponseProcessingConfig ResponseProcessing { get; set; }
    }
}
