using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Docpark.ThirdPartyIntegration.Domain.Entities;

namespace Docpark.ThirdPartyIntegration.Services.Interfaces
{
    /// <summary>
    /// 任务调度服务接口
    /// </summary>
    public interface ISchedulerService
    {
        /// <summary>
        /// 启动调度器
        /// </summary>
        Task StartAsync();

        /// <summary>
        /// 停止调度器
        /// </summary>
        Task StopAsync();

        /// <summary>
        /// 暂停调度器
        /// </summary>
        Task PauseAsync();

        /// <summary>
        /// 恢复调度器
        /// </summary>
        Task ResumeAsync();

        /// <summary>
        /// 调度API任务
        /// </summary>
        /// <param name="apiConfigId">API配置ID</param>
        /// <param name="scheduleConfig">调度配置</param>
        /// <returns>任务键</returns>
        Task<string> ScheduleApiJobAsync(string apiConfigId, ScheduleConfig scheduleConfig);

        /// <summary>
        /// 取消调度任务
        /// </summary>
        /// <param name="jobKey">任务键</param>
        /// <returns>是否成功</returns>
        Task<bool> UnscheduleJobAsync(string jobKey);

        /// <summary>
        /// 暂停任务
        /// </summary>
        /// <param name="jobKey">任务键</param>
        /// <returns>是否成功</returns>
        Task<bool> PauseJobAsync(string jobKey);

        /// <summary>
        /// 恢复任务
        /// </summary>
        /// <param name="jobKey">任务键</param>
        /// <returns>是否成功</returns>
        Task<bool> ResumeJobAsync(string jobKey);

        /// <summary>
        /// 立即执行任务
        /// </summary>
        /// <param name="jobKey">任务键</param>
        /// <returns>是否成功</returns>
        Task<bool> TriggerJobAsync(string jobKey);

        /// <summary>
        /// 更新任务调度
        /// </summary>
        /// <param name="jobKey">任务键</param>
        /// <param name="scheduleConfig">新的调度配置</param>
        /// <returns>是否成功</returns>
        Task<bool> RescheduleJobAsync(string jobKey, ScheduleConfig scheduleConfig);

        /// <summary>
        /// 获取任务状态
        /// </summary>
        /// <param name="jobKey">任务键</param>
        /// <returns>任务状态</returns>
        Task<JobStatus> GetJobStatusAsync(string jobKey);

        /// <summary>
        /// 获取所有任务状态
        /// </summary>
        /// <returns>任务状态列表</returns>
        Task<List<JobStatus>> GetAllJobStatusAsync();

        /// <summary>
        /// 获取任务执行历史
        /// </summary>
        /// <param name="jobKey">任务键</param>
        /// <param name="limit">限制数量</param>
        /// <returns>执行历史</returns>
        Task<List<JobExecutionHistory>> GetJobExecutionHistoryAsync(string jobKey, int limit = 50);

        /// <summary>
        /// 清理过期的执行历史
        /// </summary>
        /// <param name="olderThanDays">保留天数</param>
        /// <returns>清理的记录数</returns>
        Task<int> CleanupExecutionHistoryAsync(int olderThanDays = 30);

        /// <summary>
        /// 获取调度器统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        Task<SchedulerStatistics> GetSchedulerStatisticsAsync();

        /// <summary>
        /// 验证Cron表达式
        /// </summary>
        /// <param name="cronExpression">Cron表达式</param>
        /// <returns>验证结果</returns>
        Task<CronValidationResult> ValidateCronExpressionAsync(string cronExpression);

        /// <summary>
        /// 获取Cron表达式的下几次执行时间
        /// </summary>
        /// <param name="cronExpression">Cron表达式</param>
        /// <param name="count">获取数量</param>
        /// <returns>执行时间列表</returns>
        Task<List<DateTime>> GetCronNextExecutionTimesAsync(string cronExpression, int count = 5);
    }

    /// <summary>
    /// 任务状态
    /// </summary>
    public class JobStatus
    {
        /// <summary>
        /// 任务键
        /// </summary>
        public string JobKey { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string JobName { get; set; }

        /// <summary>
        /// 任务分组
        /// </summary>
        public string JobGroup { get; set; }

        /// <summary>
        /// 任务描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 任务状态
        /// </summary>
        public JobState State { get; set; }

        /// <summary>
        /// 下次执行时间
        /// </summary>
        public DateTime? NextFireTime { get; set; }

        /// <summary>
        /// 上次执行时间
        /// </summary>
        public DateTime? PreviousFireTime { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 触发器状态
        /// </summary>
        public TriggerState TriggerState { get; set; }

        /// <summary>
        /// 已执行次数
        /// </summary>
        public int ExecutionCount { get; set; }

        /// <summary>
        /// 最大执行次数
        /// </summary>
        public int MaxExecutionCount { get; set; }

        /// <summary>
        /// 是否正在运行
        /// </summary>
        public bool IsRunning { get; set; }

        /// <summary>
        /// 关联的API配置ID
        /// </summary>
        public string ApiConfigId { get; set; }
    }

    /// <summary>
    /// 任务执行历史
    /// </summary>
    public class JobExecutionHistory
    {
        /// <summary>
        /// 历史记录ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 任务键
        /// </summary>
        public string JobKey { get; set; }

        /// <summary>
        /// API配置ID
        /// </summary>
        public string ApiConfigId { get; set; }

        /// <summary>
        /// 执行开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 执行结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 执行时长（毫秒）
        /// </summary>
        public long DurationMs { get; set; }

        /// <summary>
        /// 执行状态
        /// </summary>
        public JobExecutionStatus Status { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 异常详情
        /// </summary>
        public string ExceptionDetails { get; set; }

        /// <summary>
        /// 执行结果
        /// </summary>
        public string Result { get; set; }

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; }

        /// <summary>
        /// 是否为重试执行
        /// </summary>
        public bool IsRetry { get; set; }

        /// <summary>
        /// 触发类型
        /// </summary>
        public string TriggerType { get; set; }
    }

    /// <summary>
    /// 调度器统计信息
    /// </summary>
    public class SchedulerStatistics
    {
        /// <summary>
        /// 调度器名称
        /// </summary>
        public string SchedulerName { get; set; }

        /// <summary>
        /// 调度器状态
        /// </summary>
        public string SchedulerState { get; set; }

        /// <summary>
        /// 启动时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 运行时间
        /// </summary>
        public TimeSpan? RunTime { get; set; }

        /// <summary>
        /// 总任务数
        /// </summary>
        public int TotalJobs { get; set; }

        /// <summary>
        /// 运行中的任务数
        /// </summary>
        public int RunningJobs { get; set; }

        /// <summary>
        /// 暂停的任务数
        /// </summary>
        public int PausedJobs { get; set; }

        /// <summary>
        /// 已完成的任务数
        /// </summary>
        public int CompletedJobs { get; set; }

        /// <summary>
        /// 失败的任务数
        /// </summary>
        public int FailedJobs { get; set; }

        /// <summary>
        /// 总触发器数
        /// </summary>
        public int TotalTriggers { get; set; }

        /// <summary>
        /// 今日执行次数
        /// </summary>
        public int TodayExecutions { get; set; }

        /// <summary>
        /// 今日成功次数
        /// </summary>
        public int TodaySuccesses { get; set; }

        /// <summary>
        /// 今日失败次数
        /// </summary>
        public int TodayFailures { get; set; }
    }

    /// <summary>
    /// Cron表达式验证结果
    /// </summary>
    public class CronValidationResult
    {
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 下次执行时间
        /// </summary>
        public DateTime? NextExecutionTime { get; set; }
    }

    #region 枚举定义

    /// <summary>
    /// 任务状态
    /// </summary>
    public enum JobState
    {
        /// <summary>
        /// 正常
        /// </summary>
        Normal = 0,

        /// <summary>
        /// 暂停
        /// </summary>
        Paused = 1,

        /// <summary>
        /// 完成
        /// </summary>
        Complete = 2,

        /// <summary>
        /// 错误
        /// </summary>
        Error = 3,

        /// <summary>
        /// 阻塞
        /// </summary>
        Blocked = 4,

        /// <summary>
        /// 不存在
        /// </summary>
        None = 5
    }

    /// <summary>
    /// 触发器状态
    /// </summary>
    public enum TriggerState
    {
        /// <summary>
        /// 正常
        /// </summary>
        Normal = 0,

        /// <summary>
        /// 暂停
        /// </summary>
        Paused = 1,

        /// <summary>
        /// 完成
        /// </summary>
        Complete = 2,

        /// <summary>
        /// 错误
        /// </summary>
        Error = 3,

        /// <summary>
        /// 阻塞
        /// </summary>
        Blocked = 4,

        /// <summary>
        /// 不存在
        /// </summary>
        None = 5
    }

    /// <summary>
    /// 任务执行状态
    /// </summary>
    public enum JobExecutionStatus
    {
        /// <summary>
        /// 运行中
        /// </summary>
        Running = 0,

        /// <summary>
        /// 成功
        /// </summary>
        Success = 1,

        /// <summary>
        /// 失败
        /// </summary>
        Failed = 2,

        /// <summary>
        /// 超时
        /// </summary>
        Timeout = 3,

        /// <summary>
        /// 取消
        /// </summary>
        Cancelled = 4,

        /// <summary>
        /// 重试中
        /// </summary>
        Retrying = 5
    }

    #endregion
}
